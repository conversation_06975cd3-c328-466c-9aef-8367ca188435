// DOM Elements
const messageInput = document.getElementById('messageInput');
const sendButton = document.getElementById('sendButton');
const messages = document.getElementById('messages');
const chatList = document.getElementById('chatList');
const contextMenu = document.getElementById('contextMenu');
const settingsModal = document.getElementById('settingsModal');
const renameModal = document.getElementById('renameModal');

// State
let isTyping = false;
let currentChatId = 1;
let selectedChatId = null;
let pendingResponses = new Map(); // Track pending responses per chat
let chats = {
    1: {
        id: 1,
        title: 'Новый чат',
        messages: []
    }
};

// Initialize
document.addEventListener('DOMContentLoaded', function() {
    // Check if user is logged in
    const user = getCurrentUser();
    if (!user) {
        window.location.href = 'auth.html';
        return;
    }

    // Initialize user interface
    initializeUser(user);

    // Load user's chats
    loadChatsFromStorage();

    // Check API status
    checkAPIStatus();

    messageInput.focus();
    updateSendButton();
    loadSettings();

    // Close context menu on click outside
    document.addEventListener('click', function(e) {
        if (!contextMenu.contains(e.target)) {
            contextMenu.style.display = 'none';
        }
        if (!document.getElementById('userMenu').contains(e.target) && !e.target.closest('.user-profile')) {
            document.getElementById('userMenu').classList.add('hidden');
            document.querySelector('.user-profile').classList.remove('open');
        }
    });

    // Close modals on click outside
    document.addEventListener('click', function(e) {
        if (e.target === settingsModal) {
            closeSettings();
        }
        if (e.target === renameModal) {
            closeRename();
        }
        if (e.target === document.getElementById('profileModal')) {
            closeProfile();
        }
        if (e.target === document.getElementById('exportModal')) {
            closeExportModal();
        }
    });
});

// Auto-resize textarea
function adjustTextarea() {
    messageInput.style.height = 'auto';
    messageInput.style.height = Math.min(messageInput.scrollHeight, 200) + 'px';
    updateSendButton();
}

// Update send button state
function updateSendButton() {
    const hasText = messageInput.value.trim().length > 0;
    const currentChatTyping = isTyping && pendingResponses.has(currentChatId);
    sendButton.disabled = !hasText || currentChatTyping;
    sendButton.style.opacity = hasText && !currentChatTyping ? '1' : '0.5';
}

// Handle keyboard input
function handleKeyPress(event) {
    if (event.key === 'Enter' && !event.shiftKey) {
        event.preventDefault();
        sendMessage();
    }
}

// Send message
async function sendMessage() {
    const text = messageInput.value.trim();
    if (!text || (isTyping && pendingResponses.has(currentChatId))) return;

    // Add user message
    addMessage(text, 'user');

    // Clear input
    messageInput.value = '';
    adjustTextarea();

    // Show typing indicator for current chat
    showTypingIndicator();

    // Mark this chat as having a pending response
    pendingResponses.set(currentChatId, true);

    const chatId = currentChatId; // Capture current chat ID

    try {
        // Get chat history for context
        const chatHistory = chats[chatId].messages || [];

        // Send message to GigaChat API
        const aiResponse = await sendMessageToGigaChat(text, chatHistory);

        // Add response to the correct chat
        addMessageToChat(aiResponse, 'ai', chatId);

        // Update chat title if it's the first message
        updateChatTitle(text, chatId);

    } catch (error) {
        console.error('Error getting AI response:', error);

        // Add error message
        const errorMessage = 'Извините, произошла ошибка при получении ответа. Попробуйте еще раз.';
        addMessageToChat(errorMessage, 'ai', chatId);

        showNotification('Ошибка при отправке сообщения', 'error');
    } finally {
        // Remove pending response
        pendingResponses.delete(chatId);

        // Hide typing indicator if we're viewing this chat
        if (currentChatId === chatId) {
            hideTypingIndicator();
        }
    }
}

// Add message to current chat and display
function addMessage(text, sender) {
    // Save to current chat
    chats[currentChatId].messages.push({
        text,
        sender,
        timestamp: Date.now(),
        id: Date.now() + Math.random()
    });

    // Add to DOM
    addMessageToDOM(text, sender);
}

// Add message to specific chat
function addMessageToChat(text, sender, chatId) {
    if (!chats[chatId]) return;

    chats[chatId].messages.push({
        text,
        sender,
        timestamp: Date.now(),
        id: Date.now() + Math.random()
    });

    // Auto-save chats
    saveChatsToStorage();

    // If we're viewing this chat, add to DOM
    if (currentChatId === chatId) {
        addMessageToDOM(text, sender);
    }
}

// Save chats to localStorage
function saveChatsToStorage() {
    try {
        const user = getCurrentUser();
        if (user) {
            const userChats = {
                userId: user.id,
                chats: chats,
                lastUpdated: Date.now()
            };
            localStorage.setItem(`chats_${user.id}`, JSON.stringify(userChats));
        }
    } catch (error) {
        console.error('Error saving chats:', error);
        showNotification('Ошибка сохранения чатов', 'error');
    }
}

// Load chats from localStorage
function loadChatsFromStorage() {
    try {
        const user = getCurrentUser();
        if (user) {
            const savedChats = localStorage.getItem(`chats_${user.id}`);
            if (savedChats) {
                const userChats = JSON.parse(savedChats);
                chats = userChats.chats || { 1: { id: 1, title: 'Новый чат', messages: [] } };

                // Update sidebar
                updateChatSidebar();

                // Load first chat
                const firstChatId = Object.keys(chats)[0];
                switchToChat(parseInt(firstChatId));
            }
        }
    } catch (error) {
        console.error('Error loading chats:', error);
        showNotification('Ошибка загрузки чатов', 'error');
    }
}

// Add message to DOM with action buttons
function addMessageToDOM(text, sender) {
    const messageDiv = document.createElement('div');
    messageDiv.className = 'message';

    const avatar = document.createElement('div');
    avatar.className = 'message-avatar';

    // Set avatar based on sender
    if (sender === 'user') {
        const user = getCurrentUser();
        if (user && user.profile.avatar) {
            avatar.innerHTML = `<img src="${user.profile.avatar}" alt="User Avatar">`;
        } else {
            avatar.innerHTML = `
                <svg width="20" height="20" viewBox="0 0 24 24" fill="none">
                    <path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2" stroke="currentColor" stroke-width="2"/>
                    <circle cx="12" cy="7" r="4" stroke="currentColor" stroke-width="2"/>
                </svg>
            `;
        }
    } else {
        // AI avatar
        avatar.innerHTML = `
            <svg width="20" height="20" viewBox="0 0 24 24" fill="none">
                <path d="M12 2L2 7l10 5 10-5-10-5zM2 17l10 5 10-5M2 12l10 5 10-5" stroke="currentColor" stroke-width="2"/>
            </svg>
        `;
    }

    const content = document.createElement('div');
    content.className = 'message-content';

    const textDiv = document.createElement('div');
    textDiv.className = 'message-text';
    textDiv.innerHTML = formatMessageText(text);

    content.appendChild(textDiv);

    // Add action buttons for AI messages
    if (sender === 'ai') {
        const actionsDiv = document.createElement('div');
        actionsDiv.className = 'message-actions';

        actionsDiv.innerHTML = `
            <button class="message-action-btn" onclick="likeMessage(this)" title="Нравится">
                <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
                    <path d="M7 10v12l4-4 4 4V10" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                    <path d="M7 10l5-6 5 6" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                </svg>
            </button>
            <button class="message-action-btn" onclick="dislikeMessage(this)" title="Не нравится">
                <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
                    <path d="M7 14V2l4 4 4-4v12" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                    <path d="M7 14l5 6 5-6" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                </svg>
            </button>
            <button class="message-action-btn" onclick="copyMessage(this)" title="Копировать">
                <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
                    <rect x="9" y="9" width="13" height="13" rx="2" ry="2" stroke="currentColor" stroke-width="2"/>
                    <path d="M5 15H4a2 2 0 0 1-2-2V4a2 2 0 0 1 2-2h9a2 2 0 0 1 2 2v1" stroke="currentColor" stroke-width="2"/>
                </svg>
            </button>
            <button class="message-action-btn" onclick="regenerateResponse(this)" title="Повторить ответ">
                <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
                    <polyline points="23,4 23,10 17,10" stroke="currentColor" stroke-width="2"/>
                    <path d="M20.49 15a9 9 0 1 1-2.12-9.36L23 10" stroke="currentColor" stroke-width="2"/>
                </svg>
            </button>
        `;

        content.appendChild(actionsDiv);
    }

    messageDiv.appendChild(avatar);
    messageDiv.appendChild(content);

    messages.appendChild(messageDiv);

    // Scroll to bottom smoothly
    messages.scrollTo({
        top: messages.scrollHeight,
        behavior: 'smooth'
    });
}

// Format message text with better formatting
function formatMessageText(text) {
    // Convert line breaks
    let formatted = text.replace(/\n/g, '<br>');

    // Format code blocks
    formatted = formatted.replace(/```([\s\S]*?)```/g, '<pre><code>$1</code></pre>');

    // Format inline code
    formatted = formatted.replace(/`([^`]+)`/g, '<code>$1</code>');

    // Format bold text
    formatted = formatted.replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>');

    // Format italic text
    formatted = formatted.replace(/\*(.*?)\*/g, '<em>$1</em>');

    return formatted;
}

// Message action functions
function likeMessage(button) {
    button.classList.toggle('liked');
    if (button.classList.contains('liked')) {
        button.parentElement.querySelector('.message-action-btn:nth-child(2)').classList.remove('disliked');
    }
}

function dislikeMessage(button) {
    button.classList.toggle('disliked');
    if (button.classList.contains('disliked')) {
        button.parentElement.querySelector('.message-action-btn:nth-child(1)').classList.remove('liked');
    }
}

function copyMessage(button) {
    const messageText = button.closest('.message-content').querySelector('.message-text').textContent;
    navigator.clipboard.writeText(messageText).then(() => {
        // Show feedback
        const originalHTML = button.innerHTML;
        button.innerHTML = `
            <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
                <polyline points="20,6 9,17 4,12" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            </svg>
        `;
        button.style.color = '#10b981';

        setTimeout(() => {
            button.innerHTML = originalHTML;
            button.style.color = '';
        }, 1000);
    });
}

async function regenerateResponse(button) {
    const messageDiv = button.closest('.message');
    const messageText = messageDiv.querySelector('.message-text');

    // Show loading state
    button.disabled = true;
    button.style.opacity = '0.5';
    button.classList.add('regenerating');

    try {
        // Find the user message that this AI response is replying to
        let userMessage = '';
        let currentMsg = messageDiv;

        // Look for the previous user message
        while (currentMsg.previousElementSibling) {
            currentMsg = currentMsg.previousElementSibling;
            if (currentMsg.classList.contains('message')) {
                const avatar = currentMsg.querySelector('.message-avatar img');
                if (avatar || !currentMsg.querySelector('.message-avatar svg[viewBox="0 0 24 24"]')) {
                    // This is a user message
                    userMessage = currentMsg.querySelector('.message-text').textContent;
                    break;
                }
            }
        }

        if (userMessage) {
            // Get chat history up to this point
            const allMessages = Array.from(messages.querySelectorAll('.message'));
            const messageIndex = allMessages.indexOf(messageDiv);
            const historyMessages = allMessages.slice(0, messageIndex).map(msg => {
                const isUser = msg.querySelector('.message-avatar img') || !msg.querySelector('.message-avatar svg[viewBox="0 0 24 24"]');
                return {
                    text: msg.querySelector('.message-text').textContent,
                    sender: isUser ? 'user' : 'ai'
                };
            });

            // Get new response from GigaChat
            const newResponse = await sendMessageToGigaChat(userMessage, historyMessages);
            messageText.innerHTML = formatMessageText(newResponse);

            // Update in chat data
            const chat = chats[currentChatId];
            if (chat && chat.messages) {
                const msgIndex = chat.messages.findIndex(m =>
                    m.sender === 'ai' &&
                    m.text === messageText.textContent
                );
                if (msgIndex !== -1) {
                    chat.messages[msgIndex].text = newResponse;
                    saveChatsToStorage();
                }
            }

            showNotification('Ответ обновлен!');
        } else {
            throw new Error('Не удалось найти исходное сообщение пользователя');
        }
    } catch (error) {
        console.error('Error regenerating response:', error);
        showNotification('Ошибка при обновлении ответа', 'error');
    } finally {
        // Reset button
        button.disabled = false;
        button.style.opacity = '';
        button.classList.remove('regenerating');
    }
}

// Show typing indicator
function showTypingIndicator() {
    isTyping = true;
    updateSendButton();

    const typingDiv = document.createElement('div');
    typingDiv.className = 'message typing-message';
    typingDiv.id = 'typing-indicator';

    const avatar = document.createElement('div');
    avatar.className = 'message-avatar';
    avatar.innerHTML = `
        <svg width="20" height="20" viewBox="0 0 24 24" fill="none">
            <path d="M12 2L2 7l10 5 10-5-10-5zM2 17l10 5 10-5M2 12l10 5 10-5" stroke="currentColor" stroke-width="2"/>
        </svg>
    `;

    const content = document.createElement('div');
    content.className = 'message-content typing-indicator';
    content.innerHTML = `
        <div class="typing-container">
            <div class="typing-text">AI печатает</div>
            <div class="typing-dots">
                <span></span>
                <span></span>
                <span></span>
            </div>
        </div>
    `;

    typingDiv.appendChild(avatar);
    typingDiv.appendChild(content);

    messages.appendChild(typingDiv);
    messages.scrollTo({
        top: messages.scrollHeight,
        behavior: 'smooth'
    });
}

// Hide typing indicator
function hideTypingIndicator() {
    isTyping = false;
    updateSendButton();

    const typingIndicator = document.getElementById('typing-indicator');
    if (typingIndicator) {
        typingIndicator.remove();
    }
}

// New chat
function newChat() {
    const newId = Date.now();
    chats[newId] = {
        id: newId,
        title: 'Новый чат',
        messages: []
    };

    switchToChat(newId);
    addChatToSidebar(newId, 'Новый чат');
}

// Switch to chat
function switchToChat(chatId) {
    // Hide typing indicator for previous chat
    if (currentChatId !== chatId) {
        hideTypingIndicator();
    }

    currentChatId = chatId;

    // Update active chat in sidebar
    document.querySelectorAll('.chat-item').forEach(item => {
        item.classList.remove('active');
        if (item.dataset.chatId == chatId) {
            item.classList.add('active');
        }
    });

    // Load chat messages
    loadChatMessages(chatId);

    // Update send button state
    updateSendButton();

    // Focus input
    messageInput.focus();
}

// Load chat messages
function loadChatMessages(chatId) {
    messages.innerHTML = '';

    const chat = chats[chatId];
    if (chat && chat.messages) {
        chat.messages.forEach(msg => {
            addMessageToDOM(msg.text, msg.sender);
        });
    }

    // Show typing indicator if this chat has pending response
    if (pendingResponses.has(chatId)) {
        showTypingIndicator();
    }
}

// Add chat to sidebar
function addChatToSidebar(chatId, title) {
    const chatItem = document.createElement('div');
    chatItem.className = 'chat-item active';
    chatItem.dataset.chatId = chatId;

    chatItem.innerHTML = `
        <div class="chat-content" onclick="switchToChat(${chatId})">
            <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
                <path d="M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            </svg>
            <span>${title}</span>
        </div>
        <div class="chat-actions">
            <button class="chat-menu-btn" onclick="showChatMenu(event, ${chatId})">
                <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
                    <circle cx="12" cy="12" r="1" fill="currentColor"/>
                    <circle cx="19" cy="12" r="1" fill="currentColor"/>
                    <circle cx="5" cy="12" r="1" fill="currentColor"/>
                </svg>
            </button>
        </div>
    `;

    // Remove active from other chats
    document.querySelectorAll('.chat-item').forEach(item => {
        item.classList.remove('active');
    });

    chatList.insertBefore(chatItem, chatList.firstChild);
}

// Update chat title based on first message
function updateChatTitle(firstMessage, chatId = currentChatId) {
    const chat = chats[chatId];
    if (chat && chat.messages.filter(m => m.sender === 'user').length === 1) {
        const title = firstMessage.length > 30 ?
            firstMessage.substring(0, 30) + '...' :
            firstMessage;

        chat.title = title;

        // Update sidebar
        const chatItem = document.querySelector(`[data-chat-id="${chatId}"] span`);
        if (chatItem) {
            chatItem.textContent = title;
        }
    }
}

// Chat menu functions
function showChatMenu(event, chatId) {
    event.stopPropagation();
    selectedChatId = chatId;

    contextMenu.style.display = 'block';
    contextMenu.style.left = event.pageX + 'px';
    contextMenu.style.top = event.pageY + 'px';

    // Adjust position if menu goes off screen
    const rect = contextMenu.getBoundingClientRect();
    if (rect.right > window.innerWidth) {
        contextMenu.style.left = (event.pageX - rect.width) + 'px';
    }
    if (rect.bottom > window.innerHeight) {
        contextMenu.style.top = (event.pageY - rect.height) + 'px';
    }
}

function renameChat() {
    contextMenu.style.display = 'none';
    const chat = chats[selectedChatId];
    if (chat) {
        document.getElementById('chatNameInput').value = chat.title;
        renameModal.classList.add('show');
        document.getElementById('chatNameInput').focus();
    }
}

function deleteChat() {
    contextMenu.style.display = 'none';
    if (Object.keys(chats).length <= 1) {
        showNotification('Нельзя удалить последний чат', 'warning');
        return;
    }

    if (confirm('Вы уверены, что хотите удалить этот чат?')) {
        // Remove from chats object
        delete chats[selectedChatId];

        // Save changes
        saveChatsToStorage();

        // Update sidebar
        updateChatSidebar();

        // Switch to another chat if current was deleted
        if (currentChatId == selectedChatId) {
            const remainingChats = Object.keys(chats);
            if (remainingChats.length > 0) {
                switchToChat(parseInt(remainingChats[0]));
            }
        }

        showNotification('Чат удален');
    }
}

// Export chat functionality
function exportChat(chatId, format = 'txt') {
    const chat = chats[chatId];
    if (!chat) return;

    const user = getCurrentUser();
    let content = '';

    if (format === 'txt') {
        content = `Экспорт чата: ${chat.title}\n`;
        content += `Пользователь: ${user.profile.displayName || user.name}\n`;
        content += `Дата экспорта: ${new Date().toLocaleString('ru-RU')}\n`;
        content += `${'='.repeat(50)}\n\n`;

        chat.messages.forEach(msg => {
            const timestamp = new Date(msg.timestamp).toLocaleString('ru-RU');
            const sender = msg.sender === 'user' ? user.profile.displayName || user.name : 'AI Assistant';
            content += `[${timestamp}] ${sender}:\n${msg.text}\n\n`;
        });
    } else if (format === 'json') {
        content = JSON.stringify({
            chatTitle: chat.title,
            user: {
                name: user.profile.displayName || user.name,
                email: user.email
            },
            exportDate: new Date().toISOString(),
            messages: chat.messages
        }, null, 2);
    }

    // Download file
    const blob = new Blob([content], { type: format === 'json' ? 'application/json' : 'text/plain' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `chat_${chat.title.replace(/[^a-zA-Z0-9]/g, '_')}.${format}`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);

    showNotification(`Чат экспортирован в формате ${format.toUpperCase()}`);
}

// Export all chats
function exportAllChats() {
    const user = getCurrentUser();
    const exportData = {
        user: {
            name: user.profile.displayName || user.name,
            email: user.email
        },
        exportDate: new Date().toISOString(),
        chats: Object.values(chats).map(chat => ({
            title: chat.title,
            messages: chat.messages,
            createdAt: chat.createdAt || new Date().toISOString()
        }))
    };

    const content = JSON.stringify(exportData, null, 2);
    const blob = new Blob([content], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `all_chats_${new Date().toISOString().split('T')[0]}.json`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);

    showNotification('Все чаты экспортированы');
}

// Export modal functions
function showExportOptions() {
    contextMenu.style.display = 'none';
    document.getElementById('exportModal').classList.add('show');
}

function closeExportModal() {
    document.getElementById('exportModal').classList.remove('show');
}

function exportCurrentChat(format) {
    exportChat(selectedChatId, format);
    closeExportModal();
}

// Modal functions
function openSettings() {
    settingsModal.classList.add('show');
}

function closeSettings() {
    settingsModal.classList.remove('show');
}

function closeRename() {
    renameModal.classList.remove('show');
}

function confirmRename() {
    const newName = document.getElementById('chatNameInput').value.trim();
    if (newName && selectedChatId) {
        chats[selectedChatId].title = newName;
        const chatItem = document.querySelector(`[data-chat-id="${selectedChatId}"] span`);
        if (chatItem) {
            chatItem.textContent = newName;
        }
    }
    closeRename();
}

// Settings functions
function loadSettings() {
    const aiModel = localStorage.getItem('aiModel') || 'gpt-4';
    const theme = localStorage.getItem('theme') || 'light';
    const temperature = localStorage.getItem('temperature') || '0.7';

    document.getElementById('aiModel').value = aiModel;
    document.getElementById('theme').value = theme;
    document.getElementById('temperature').value = temperature;
    document.getElementById('temperatureValue').textContent = temperature;

    // Apply theme
    if (theme === 'dark') {
        document.body.classList.add('dark-theme');
    }
}

function saveSettings() {
    const aiModel = document.getElementById('aiModel').value;
    const theme = document.getElementById('theme').value;
    const temperature = document.getElementById('temperature').value;

    localStorage.setItem('aiModel', aiModel);
    localStorage.setItem('theme', theme);
    localStorage.setItem('temperature', temperature);

    // Apply theme
    document.body.classList.toggle('dark-theme', theme === 'dark');
}

// Event listeners
messageInput.addEventListener('input', adjustTextarea);

// Settings event listeners
document.getElementById('temperature').addEventListener('input', function() {
    document.getElementById('temperatureValue').textContent = this.value;
});

document.getElementById('aiModel').addEventListener('change', saveSettings);
document.getElementById('theme').addEventListener('change', saveSettings);
document.getElementById('temperature').addEventListener('change', saveSettings);

// Focus input when clicking anywhere in the input area
document.querySelector('.input-section').addEventListener('click', () => {
    messageInput.focus();
});

// User management functions
function getCurrentUser() {
    const user = localStorage.getItem('currentUser') || sessionStorage.getItem('currentUser');
    return user ? JSON.parse(user) : null;
}

function initializeUser(user) {
    // Update user info in sidebar
    document.getElementById('userName').textContent = user.profile.displayName || user.name;
    document.getElementById('userEmail').textContent = user.email;

    // Update avatar
    const avatarSmall = document.getElementById('userAvatarSmall');
    if (user.profile.avatar) {
        avatarSmall.innerHTML = `<img src="${user.profile.avatar}" alt="Avatar">`;
    }

    // Add personalized welcome message if no chats exist
    if (Object.keys(chats).length === 1 && chats[1].messages.length === 0) {
        const welcomeMessage = `Привет, ${user.profile.displayName || user.name}! 👋

Я ваш AI-помощник, готов помочь вам с любыми задачами. Вот что я умею:

• **Отвечать на вопросы** - задавайте любые вопросы
• **Помогать с кодом** - написание, отладка, объяснение
• **Создавать контент** - тексты, идеи, планы
• **Анализировать данные** - обработка информации
• **Обучать** - объяснение сложных концепций

Просто начните печатать ваш вопрос внизу! 💬`;

        addMessageToDOM(welcomeMessage, 'ai');
    }
}

function toggleUserMenu() {
    const userMenu = document.getElementById('userMenu');
    const userProfile = document.querySelector('.user-profile');

    userMenu.classList.toggle('hidden');
    userProfile.classList.toggle('open');
}

function openProfile() {
    const user = getCurrentUser();
    if (!user) return;

    // Hide user menu
    document.getElementById('userMenu').classList.add('hidden');
    document.querySelector('.user-profile').classList.remove('open');

    // Populate profile modal
    document.getElementById('profileDisplayName').value = user.profile.displayName || user.name;
    document.getElementById('profileEmail').value = user.email;
    document.getElementById('profileBio').value = user.profile.bio || '';

    // Update avatar
    const profileAvatar = document.getElementById('profileAvatar');
    if (user.profile.avatar) {
        profileAvatar.innerHTML = `<img src="${user.profile.avatar}" alt="Avatar">`;
    }

    // Update interests
    const interestsContainer = document.getElementById('profileInterests');
    interestsContainer.innerHTML = '';
    if (user.profile.interests && user.profile.interests.length > 0) {
        user.profile.interests.forEach(interest => {
            const badge = document.createElement('span');
            badge.className = 'interest-badge';
            badge.textContent = getInterestLabel(interest);
            interestsContainer.appendChild(badge);
        });
    } else {
        interestsContainer.innerHTML = '<span style="color: #6b7280; font-style: italic;">Интересы не указаны</span>';
    }

    // Update stats
    updateProfileStats(user);

    // Show modal
    document.getElementById('profileModal').classList.add('show');
}

function closeProfile() {
    document.getElementById('profileModal').classList.remove('show');
}

function changeAvatar() {
    document.getElementById('profileAvatarInput').click();
}

function updateProfileAvatar(event) {
    const file = event.target.files[0];
    if (file) {
        const reader = new FileReader();
        reader.onload = function(e) {
            const profileAvatar = document.getElementById('profileAvatar');
            profileAvatar.innerHTML = `<img src="${e.target.result}" alt="Avatar">`;
        };
        reader.readAsDataURL(file);
    }
}

function saveProfile() {
    const user = getCurrentUser();
    if (!user) return;

    // Get form values
    const displayName = document.getElementById('profileDisplayName').value.trim();
    const bio = document.getElementById('profileBio').value.trim();

    // Get avatar if changed
    const avatarImg = document.querySelector('#profileAvatar img');
    if (avatarImg) {
        user.profile.avatar = avatarImg.src;
    }

    // Update user profile
    user.profile.displayName = displayName;
    user.profile.bio = bio;

    // Save to storage
    const storage = localStorage.getItem('currentUser') ? localStorage : sessionStorage;
    storage.setItem('currentUser', JSON.stringify(user));

    // Update users array
    const users = JSON.parse(localStorage.getItem('users') || '[]');
    const userIndex = users.findIndex(u => u.id === user.id);
    if (userIndex !== -1) {
        users[userIndex] = user;
        localStorage.setItem('users', JSON.stringify(users));
    }

    // Update UI
    initializeUser(user);

    // Close modal
    closeProfile();

    // Show success message
    showNotification('Профиль успешно обновлен!');
}

function updateProfileStats(user) {
    // Count chats and messages
    const totalChats = Object.keys(chats).length;
    let totalMessages = 0;
    Object.values(chats).forEach(chat => {
        totalMessages += chat.messages.length;
    });

    // Format member since date
    const memberSince = new Date(user.createdAt).toLocaleDateString('ru-RU', {
        month: 'short',
        year: 'numeric'
    });

    document.getElementById('totalChats').textContent = totalChats;
    document.getElementById('totalMessages').textContent = totalMessages;
    document.getElementById('memberSince').textContent = memberSince;
}

function getInterestLabel(interest) {
    const labels = {
        'programming': 'Программирование',
        'design': 'Дизайн',
        'writing': 'Писательство',
        'business': 'Бизнес',
        'education': 'Образование',
        'science': 'Наука'
    };
    return labels[interest] || interest;
}

function logout() {
    // Clear user data
    localStorage.removeItem('currentUser');
    sessionStorage.removeItem('currentUser');

    // Redirect to auth page
    window.location.href = 'auth.html';
}

// Update chat sidebar
function updateChatSidebar() {
    const chatList = document.getElementById('chatList');
    chatList.innerHTML = '';

    Object.values(chats).forEach(chat => {
        const chatItem = document.createElement('div');
        chatItem.className = 'chat-item';
        chatItem.dataset.chatId = chat.id;

        if (chat.id === currentChatId) {
            chatItem.classList.add('active');
        }

        chatItem.innerHTML = `
            <div class="chat-content" onclick="switchToChat(${chat.id})">
                <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
                    <path d="M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                </svg>
                <span>${chat.title}</span>
            </div>
            <div class="chat-actions">
                <button class="chat-menu-btn" onclick="showChatMenu(event, ${chat.id})">
                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
                        <circle cx="12" cy="12" r="1" fill="currentColor"/>
                        <circle cx="19" cy="12" r="1" fill="currentColor"/>
                        <circle cx="5" cy="12" r="1" fill="currentColor"/>
                    </svg>
                </button>
            </div>
        `;

        chatList.appendChild(chatItem);
    });
}

function showNotification(message, type = 'success') {
    // Create notification element
    const notification = document.createElement('div');
    notification.className = `notification ${type}`;

    const colors = {
        success: '#10b981',
        error: '#ef4444',
        warning: '#f59e0b',
        info: '#3b82f6'
    };

    const icons = {
        success: '✓',
        error: '✕',
        warning: '⚠',
        info: 'ℹ'
    };

    notification.innerHTML = `
        <div class="notification-icon">${icons[type]}</div>
        <div class="notification-message">${message}</div>
    `;

    notification.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        background: ${colors[type]};
        color: white;
        padding: 12px 16px;
        border-radius: 8px;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
        z-index: 1000;
        animation: slideIn 0.3s ease;
        display: flex;
        align-items: center;
        gap: 8px;
        font-size: 14px;
        font-weight: 500;
        max-width: 300px;
    `;

    document.body.appendChild(notification);

    // Remove after 3 seconds
    setTimeout(() => {
        notification.style.animation = 'slideOut 0.3s ease';
        setTimeout(() => notification.remove(), 300);
    }, 3000);
}

// Add notification animations
const notificationStyle = document.createElement('style');
notificationStyle.textContent = `
    @keyframes slideIn {
        from {
            transform: translateX(100%);
            opacity: 0;
        }
        to {
            transform: translateX(0);
            opacity: 1;
        }
    }

    @keyframes slideOut {
        from {
            transform: translateX(0);
            opacity: 1;
        }
        to {
            transform: translateX(100%);
            opacity: 0;
        }
    }
`;
document.head.appendChild(notificationStyle);

// API Status Management
async function checkAPIStatus() {
    const statusIndicator = document.getElementById('statusIndicator');
    const statusText = document.getElementById('statusText');

    if (!statusIndicator || !statusText) return;

    // Set checking state
    statusIndicator.className = 'status-indicator checking';
    statusText.textContent = 'Проверка подключения...';

    try {
        if (typeof window.gigaChatAPI !== 'undefined') {
            const status = await window.gigaChatAPI.checkStatus();

            if (status.status === 'connected') {
                statusIndicator.className = 'status-indicator connected';
                statusText.textContent = 'Подключено к GigaChat';
            } else if (status.status === 'demo') {
                statusIndicator.className = 'status-indicator checking';
                statusText.textContent = 'Демо-режим (умные ответы)';
            } else {
                statusIndicator.className = 'status-indicator error';
                statusText.textContent = 'Ошибка подключения';
            }
        } else {
            statusIndicator.className = 'status-indicator error';
            statusText.textContent = 'API не загружен';
        }
    } catch (error) {
        statusIndicator.className = 'status-indicator error';
        statusText.textContent = 'Ошибка подключения';
        console.error('API Status Check Error:', error);
    }
}

// Update API status when settings modal opens
function openSettings() {
    settingsModal.classList.add('show');
    // Recheck API status when opening settings
    setTimeout(checkAPIStatus, 100);
}

// Enhanced error handling for API calls
function handleAPIError(error, context = '') {
    console.error(`API Error ${context}:`, error);

    let errorMessage = 'Произошла ошибка при обращении к ИИ.';

    if (error.message.includes('401') || error.message.includes('403')) {
        errorMessage = 'Ошибка авторизации. Проверьте API ключ.';
    } else if (error.message.includes('429')) {
        errorMessage = 'Превышен лимит запросов. Попробуйте позже.';
    } else if (error.message.includes('network') || error.message.includes('fetch')) {
        errorMessage = 'Ошибка сети. Проверьте подключение к интернету.';
    } else if (error.message.includes('timeout')) {
        errorMessage = 'Превышено время ожидания ответа.';
    }

    return errorMessage;
}

// Enhanced message sending with better error handling
async function sendMessageWithRetry(text, chatHistory, maxRetries = 2) {
    let lastError;

    for (let attempt = 1; attempt <= maxRetries; attempt++) {
        try {
            const response = await sendMessageToGigaChat(text, chatHistory);
            return response;
        } catch (error) {
            lastError = error;
            console.warn(`Attempt ${attempt} failed:`, error);

            if (attempt < maxRetries) {
                // Wait before retry (exponential backoff)
                await new Promise(resolve => setTimeout(resolve, 1000 * attempt));
            }
        }
    }

    throw lastError;
}

// Auto-retry failed messages
async function retryFailedMessage(messageElement) {
    const messageText = messageElement.querySelector('.message-text');
    const originalText = messageText.textContent;

    if (originalText.includes('Ошибка') || originalText.includes('Извините')) {
        // Find the user message this was responding to
        let userMessage = '';
        let currentMsg = messageElement;

        while (currentMsg.previousElementSibling) {
            currentMsg = currentMsg.previousElementSibling;
            if (currentMsg.classList.contains('message')) {
                const avatar = currentMsg.querySelector('.message-avatar img');
                if (avatar || !currentMsg.querySelector('.message-avatar svg[viewBox="0 0 24 24"]')) {
                    userMessage = currentMsg.querySelector('.message-text').textContent;
                    break;
                }
            }
        }

        if (userMessage) {
            try {
                messageText.innerHTML = '<div class="typing-container"><div class="typing-text">Повторная попытка...</div></div>';

                const response = await sendMessageWithRetry(userMessage, []);
                messageText.innerHTML = formatMessageText(response);

                showNotification('Сообщение успешно отправлено повторно');
            } catch (error) {
                messageText.innerHTML = formatMessageText(handleAPIError(error));
                showNotification('Повторная попытка не удалась', 'error');
            }
        }
    }
}

// Keyboard shortcuts
document.addEventListener('keydown', function(e) {
    if ((e.ctrlKey || e.metaKey) && e.key === 'k') {
        e.preventDefault();
        newChat();
    }

    // Ctrl+R to retry last failed message
    if ((e.ctrlKey || e.metaKey) && e.key === 'r') {
        e.preventDefault();
        const lastMessage = messages.lastElementChild;
        if (lastMessage && lastMessage.classList.contains('message')) {
            retryFailedMessage(lastMessage);
        }
    }
});
