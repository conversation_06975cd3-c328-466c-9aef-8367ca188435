// DOM Elements
const messageInput = document.getElementById('messageInput');
const sendButton = document.getElementById('sendButton');
const messages = document.getElementById('messages');
const chatList = document.getElementById('chatList');
const contextMenu = document.getElementById('contextMenu');
const settingsModal = document.getElementById('settingsModal');
const renameModal = document.getElementById('renameModal');

// State
let isTyping = false;
let currentChatId = 1;
let selectedChatId = null;
let pendingResponses = new Map(); // Track pending responses per chat
let chats = {
    1: {
        id: 1,
        title: 'Новый чат',
        messages: []
    }
};

// Initialize
document.addEventListener('DOMContentLoaded', function() {
    messageInput.focus();
    updateSendButton();
    loadSettings();

    // Close context menu on click outside
    document.addEventListener('click', function(e) {
        if (!contextMenu.contains(e.target)) {
            contextMenu.style.display = 'none';
        }
    });

    // Close modals on click outside
    document.addEventListener('click', function(e) {
        if (e.target === settingsModal) {
            closeSettings();
        }
        if (e.target === renameModal) {
            closeRename();
        }
    });
});

// Auto-resize textarea
function adjustTextarea() {
    messageInput.style.height = 'auto';
    messageInput.style.height = Math.min(messageInput.scrollHeight, 200) + 'px';
    updateSendButton();
}

// Update send button state
function updateSendButton() {
    const hasText = messageInput.value.trim().length > 0;
    const currentChatTyping = isTyping && pendingResponses.has(currentChatId);
    sendButton.disabled = !hasText || currentChatTyping;
    sendButton.style.opacity = hasText && !currentChatTyping ? '1' : '0.5';
}

// Handle keyboard input
function handleKeyPress(event) {
    if (event.key === 'Enter' && !event.shiftKey) {
        event.preventDefault();
        sendMessage();
    }
}

// Send message
function sendMessage() {
    const text = messageInput.value.trim();
    if (!text || (isTyping && pendingResponses.has(currentChatId))) return;

    // Add user message
    addMessage(text, 'user');

    // Clear input
    messageInput.value = '';
    adjustTextarea();

    // Show typing indicator for current chat
    showTypingIndicator();

    // Mark this chat as having a pending response
    pendingResponses.set(currentChatId, true);

    // Simulate AI response
    const responseDelay = 1000 + Math.random() * 2000;
    const chatId = currentChatId; // Capture current chat ID

    setTimeout(() => {
        // Only add response if we're still in the same chat or if switching back
        const responses = [
            "Понял ваш вопрос. Вот что я могу сказать по этому поводу...",
            "Интересная задача! Давайте разберем это пошагово.",
            "Хороший вопрос. Позвольте мне объяснить подробнее.",
            "Я помогу вам с этим. Вот мой ответ:",
            "Отлично! Вот что я думаю об этом:"
        ];

        const randomResponse = responses[Math.floor(Math.random() * responses.length)];

        // Add response to the correct chat
        addMessageToChat(randomResponse, 'ai', chatId);

        // Remove pending response
        pendingResponses.delete(chatId);

        // Hide typing indicator if we're viewing this chat
        if (currentChatId === chatId) {
            hideTypingIndicator();
        }

        // Update chat title if it's the first message
        updateChatTitle(text, chatId);

    }, responseDelay);
}

// Add message to current chat and display
function addMessage(text, sender) {
    addMessageToChat(text, sender, currentChatId);

    // Only update display if we're viewing the current chat
    if (currentChatId === currentChatId) {
        addMessageToDOM(text, sender);
    }
}

// Add message to specific chat
function addMessageToChat(text, sender, chatId) {
    if (!chats[chatId]) return;

    chats[chatId].messages.push({
        text,
        sender,
        timestamp: Date.now(),
        id: Date.now() + Math.random()
    });

    // If we're viewing this chat, add to DOM
    if (currentChatId === chatId) {
        addMessageToDOM(text, sender);
    }
}

// Add message to DOM with action buttons
function addMessageToDOM(text, sender) {
    const messageDiv = document.createElement('div');
    messageDiv.className = 'message';

    const avatar = document.createElement('div');
    avatar.className = 'message-avatar';
    avatar.textContent = sender === 'user' ? 'Вы' : 'AI';

    const content = document.createElement('div');
    content.className = 'message-content';

    const textDiv = document.createElement('div');
    textDiv.className = 'message-text';
    textDiv.innerHTML = text.replace(/\n/g, '<br>');

    content.appendChild(textDiv);

    // Add action buttons for AI messages
    if (sender === 'ai') {
        const actionsDiv = document.createElement('div');
        actionsDiv.className = 'message-actions';

        actionsDiv.innerHTML = `
            <button class="message-action-btn" onclick="likeMessage(this)" title="Нравится">
                <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
                    <path d="M7 10v12l4-4 4 4V10" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                    <path d="M7 10l5-6 5 6" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                </svg>
            </button>
            <button class="message-action-btn" onclick="dislikeMessage(this)" title="Не нравится">
                <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
                    <path d="M7 14V2l4 4 4-4v12" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                    <path d="M7 14l5 6 5-6" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                </svg>
            </button>
            <button class="message-action-btn" onclick="copyMessage(this)" title="Копировать">
                <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
                    <rect x="9" y="9" width="13" height="13" rx="2" ry="2" stroke="currentColor" stroke-width="2"/>
                    <path d="M5 15H4a2 2 0 0 1-2-2V4a2 2 0 0 1 2-2h9a2 2 0 0 1 2 2v1" stroke="currentColor" stroke-width="2"/>
                </svg>
            </button>
        `;

        content.appendChild(actionsDiv);
    }

    messageDiv.appendChild(avatar);
    messageDiv.appendChild(content);

    messages.appendChild(messageDiv);

    // Scroll to bottom
    messages.scrollTop = messages.scrollHeight;
}

// Message action functions
function likeMessage(button) {
    button.classList.toggle('liked');
    if (button.classList.contains('liked')) {
        button.parentElement.querySelector('.message-action-btn:nth-child(2)').classList.remove('disliked');
    }
}

function dislikeMessage(button) {
    button.classList.toggle('disliked');
    if (button.classList.contains('disliked')) {
        button.parentElement.querySelector('.message-action-btn:nth-child(1)').classList.remove('liked');
    }
}

function copyMessage(button) {
    const messageText = button.closest('.message-content').querySelector('.message-text').textContent;
    navigator.clipboard.writeText(messageText).then(() => {
        // Show feedback
        const originalHTML = button.innerHTML;
        button.innerHTML = `
            <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
                <polyline points="20,6 9,17 4,12" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            </svg>
        `;
        button.style.color = '#10b981';

        setTimeout(() => {
            button.innerHTML = originalHTML;
            button.style.color = '';
        }, 1000);
    });
}

// Show typing indicator
function showTypingIndicator() {
    isTyping = true;
    updateSendButton();

    const typingDiv = document.createElement('div');
    typingDiv.className = 'message typing-message';
    typingDiv.id = 'typing-indicator';

    const avatar = document.createElement('div');
    avatar.className = 'message-avatar';
    avatar.textContent = 'AI';

    const content = document.createElement('div');
    content.className = 'message-content typing-indicator';
    content.innerHTML = `
        <div class="typing-dots">
            <span></span>
            <span></span>
            <span></span>
        </div>
    `;

    typingDiv.appendChild(avatar);
    typingDiv.appendChild(content);

    messages.appendChild(typingDiv);
    messages.scrollTop = messages.scrollHeight;
}

// Hide typing indicator
function hideTypingIndicator() {
    isTyping = false;
    updateSendButton();

    const typingIndicator = document.getElementById('typing-indicator');
    if (typingIndicator) {
        typingIndicator.remove();
    }
}

// New chat
function newChat() {
    const newId = Date.now();
    chats[newId] = {
        id: newId,
        title: 'Новый чат',
        messages: []
    };

    switchToChat(newId);
    addChatToSidebar(newId, 'Новый чат');
}

// Switch to chat
function switchToChat(chatId) {
    // Hide typing indicator for previous chat
    if (currentChatId !== chatId) {
        hideTypingIndicator();
    }

    currentChatId = chatId;

    // Update active chat in sidebar
    document.querySelectorAll('.chat-item').forEach(item => {
        item.classList.remove('active');
        if (item.dataset.chatId == chatId) {
            item.classList.add('active');
        }
    });

    // Load chat messages
    loadChatMessages(chatId);

    // Update send button state
    updateSendButton();

    // Focus input
    messageInput.focus();
}

// Load chat messages
function loadChatMessages(chatId) {
    messages.innerHTML = '';

    const chat = chats[chatId];
    if (chat && chat.messages) {
        chat.messages.forEach(msg => {
            addMessageToDOM(msg.text, msg.sender);
        });
    }

    // Show typing indicator if this chat has pending response
    if (pendingResponses.has(chatId)) {
        showTypingIndicator();
    }
}

// Add chat to sidebar
function addChatToSidebar(chatId, title) {
    const chatItem = document.createElement('div');
    chatItem.className = 'chat-item active';
    chatItem.dataset.chatId = chatId;

    chatItem.innerHTML = `
        <div class="chat-content" onclick="switchToChat(${chatId})">
            <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
                <path d="M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            </svg>
            <span>${title}</span>
        </div>
        <div class="chat-actions">
            <button class="chat-menu-btn" onclick="showChatMenu(event, ${chatId})">
                <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
                    <circle cx="12" cy="12" r="1" fill="currentColor"/>
                    <circle cx="19" cy="12" r="1" fill="currentColor"/>
                    <circle cx="5" cy="12" r="1" fill="currentColor"/>
                </svg>
            </button>
        </div>
    `;

    // Remove active from other chats
    document.querySelectorAll('.chat-item').forEach(item => {
        item.classList.remove('active');
    });

    chatList.insertBefore(chatItem, chatList.firstChild);
}

// Update chat title based on first message
function updateChatTitle(firstMessage, chatId = currentChatId) {
    const chat = chats[chatId];
    if (chat && chat.messages.filter(m => m.sender === 'user').length === 1) {
        const title = firstMessage.length > 30 ?
            firstMessage.substring(0, 30) + '...' :
            firstMessage;

        chat.title = title;

        // Update sidebar
        const chatItem = document.querySelector(`[data-chat-id="${chatId}"] span`);
        if (chatItem) {
            chatItem.textContent = title;
        }
    }
}

// Chat menu functions
function showChatMenu(event, chatId) {
    event.stopPropagation();
    selectedChatId = chatId;

    contextMenu.style.display = 'block';
    contextMenu.style.left = event.pageX + 'px';
    contextMenu.style.top = event.pageY + 'px';

    // Adjust position if menu goes off screen
    const rect = contextMenu.getBoundingClientRect();
    if (rect.right > window.innerWidth) {
        contextMenu.style.left = (event.pageX - rect.width) + 'px';
    }
    if (rect.bottom > window.innerHeight) {
        contextMenu.style.top = (event.pageY - rect.height) + 'px';
    }
}

function renameChat() {
    contextMenu.style.display = 'none';
    const chat = chats[selectedChatId];
    if (chat) {
        document.getElementById('chatNameInput').value = chat.title;
        renameModal.classList.add('show');
        document.getElementById('chatNameInput').focus();
    }
}

function deleteChat() {
    contextMenu.style.display = 'none';
    if (Object.keys(chats).length <= 1) {
        alert('Нельзя удалить последний чат');
        return;
    }

    if (confirm('Вы уверены, что хотите удалить этот чат?')) {
        // Remove from chats object
        delete chats[selectedChatId];

        // Remove from sidebar
        const chatItem = document.querySelector(`[data-chat-id="${selectedChatId}"]`);
        if (chatItem) {
            chatItem.remove();
        }

        // Switch to another chat if current was deleted
        if (currentChatId == selectedChatId) {
            const remainingChats = Object.keys(chats);
            if (remainingChats.length > 0) {
                switchToChat(parseInt(remainingChats[0]));
                document.querySelector(`[data-chat-id="${remainingChats[0]}"]`).classList.add('active');
            }
        }
    }
}

// Modal functions
function openSettings() {
    settingsModal.classList.add('show');
}

function closeSettings() {
    settingsModal.classList.remove('show');
}

function closeRename() {
    renameModal.classList.remove('show');
}

function confirmRename() {
    const newName = document.getElementById('chatNameInput').value.trim();
    if (newName && selectedChatId) {
        chats[selectedChatId].title = newName;
        const chatItem = document.querySelector(`[data-chat-id="${selectedChatId}"] span`);
        if (chatItem) {
            chatItem.textContent = newName;
        }
    }
    closeRename();
}

// Settings functions
function loadSettings() {
    const aiModel = localStorage.getItem('aiModel') || 'gpt-4';
    const theme = localStorage.getItem('theme') || 'light';
    const temperature = localStorage.getItem('temperature') || '0.7';

    document.getElementById('aiModel').value = aiModel;
    document.getElementById('theme').value = theme;
    document.getElementById('temperature').value = temperature;
    document.getElementById('temperatureValue').textContent = temperature;

    // Apply theme
    if (theme === 'dark') {
        document.body.classList.add('dark-theme');
    }
}

function saveSettings() {
    const aiModel = document.getElementById('aiModel').value;
    const theme = document.getElementById('theme').value;
    const temperature = document.getElementById('temperature').value;

    localStorage.setItem('aiModel', aiModel);
    localStorage.setItem('theme', theme);
    localStorage.setItem('temperature', temperature);

    // Apply theme
    document.body.classList.toggle('dark-theme', theme === 'dark');
}

// Event listeners
messageInput.addEventListener('input', adjustTextarea);

// Settings event listeners
document.getElementById('temperature').addEventListener('input', function() {
    document.getElementById('temperatureValue').textContent = this.value;
});

document.getElementById('aiModel').addEventListener('change', saveSettings);
document.getElementById('theme').addEventListener('change', saveSettings);
document.getElementById('temperature').addEventListener('change', saveSettings);

// Focus input when clicking anywhere in the input area
document.querySelector('.input-section').addEventListener('click', () => {
    messageInput.focus();
});

// Keyboard shortcuts
document.addEventListener('keydown', function(e) {
    if ((e.ctrlKey || e.metaKey) && e.key === 'k') {
        e.preventDefault();
        newChat();
    }
});
