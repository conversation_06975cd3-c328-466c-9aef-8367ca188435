* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    background: #f7f7f8;
    color: #374151;
    height: 100vh;
    overflow: hidden;
}

.app {
    display: flex;
    height: 100vh;
}

/* Sidebar */
.sidebar {
    width: 260px;
    background: #171717;
    color: white;
    display: flex;
    flex-direction: column;
    border-right: 1px solid #2d2d2d;
}

.sidebar-header {
    padding: 12px;
    border-bottom: 1px solid #2d2d2d;
}

.new-chat-btn {
    width: 100%;
    background: transparent;
    color: white;
    border: 1px solid #4d4d4f;
    padding: 12px 16px;
    border-radius: 6px;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 14px;
    transition: background-color 0.2s;
}

.new-chat-btn:hover {
    background: #2d2d2d;
}

.chat-list {
    flex: 1;
    overflow-y: auto;
    padding: 8px;
}

.chat-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 8px 12px;
    border-radius: 6px;
    margin-bottom: 2px;
    color: #ececf1;
    font-size: 14px;
    transition: background-color 0.2s;
    position: relative;
}

.chat-item:hover {
    background: #2d2d2d;
}

.chat-item.active {
    background: #343541;
}

.chat-content {
    display: flex;
    align-items: center;
    gap: 12px;
    flex: 1;
    cursor: pointer;
    min-width: 0;
}

.chat-content span {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.chat-actions {
    opacity: 0;
    transition: opacity 0.2s;
}

.chat-item:hover .chat-actions {
    opacity: 1;
}

.chat-menu-btn {
    background: none;
    border: none;
    color: #ececf1;
    cursor: pointer;
    padding: 4px;
    border-radius: 4px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.chat-menu-btn:hover {
    background: #4d4d4f;
}

.sidebar-footer {
    padding: 12px;
    border-top: 1px solid #2d2d2d;
}

.settings-btn {
    width: 100%;
    background: none;
    border: 1px solid #4d4d4f;
    color: #ececf1;
    padding: 12px 16px;
    border-radius: 6px;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 14px;
    transition: background-color 0.2s;
}

.settings-btn:hover {
    background: #2d2d2d;
}

/* User Profile in Sidebar */
.user-profile {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 12px;
    border-radius: 8px;
    cursor: pointer;
    transition: background-color 0.2s;
    position: relative;
}

.user-profile:hover {
    background: #2d2d2d;
}

.user-avatar-small {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    background: #4f46e5;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    overflow: hidden;
    flex-shrink: 0;
}

.user-avatar-small img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.user-info {
    flex: 1;
    min-width: 0;
}

.user-name {
    font-size: 14px;
    font-weight: 500;
    color: #ececf1;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.user-email {
    font-size: 12px;
    color: #9ca3af;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.chevron {
    color: #9ca3af;
    transition: transform 0.2s;
}

.user-profile.open .chevron {
    transform: rotate(180deg);
}

/* User Menu */
.user-menu {
    position: absolute;
    bottom: 100%;
    left: 0;
    right: 0;
    background: #2d2d2d;
    border: 1px solid #4d4d4f;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
    margin-bottom: 8px;
    z-index: 100;
}

.user-menu.hidden {
    display: none;
}

.user-menu-item {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 12px 16px;
    color: #ececf1;
    cursor: pointer;
    transition: background-color 0.2s;
    font-size: 14px;
}

.user-menu-item:hover {
    background: #3d3d3f;
}

.user-menu-item.logout {
    color: #ef4444;
}

.user-menu-item.logout:hover {
    background: #2d1b1b;
}

.user-menu-divider {
    height: 1px;
    background: #4d4d4f;
    margin: 4px 0;
}

/* Main Content */
.main-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    background: white;
}

.chat-container {
    flex: 1;
    overflow-y: auto;
    padding: 0;
}

.messages {
    max-width: 768px;
    margin: 0 auto;
    padding: 24px;
}

.message {
    margin-bottom: 24px;
    display: flex;
    gap: 16px;
}

.message-avatar {
    width: 32px;
    height: 32px;
    border-radius: 4px;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
    font-size: 16px;
    font-weight: 600;
}

.user-message .message-avatar {
    background: #19c37d;
    color: white;
}

.ai-message .message-avatar {
    background: #ab68ff;
    color: white;
}

.message-content {
    flex: 1;
    line-height: 1.6;
    font-size: 16px;
    position: relative;
}

.message-text {
    margin-bottom: 8px;
}

.message-actions {
    display: flex;
    gap: 8px;
    opacity: 0;
    transition: opacity 0.2s;
    margin-top: 8px;
}

.message:hover .message-actions {
    opacity: 1;
}

.message-action-btn {
    background: none;
    border: none;
    color: #6b7280;
    cursor: pointer;
    padding: 4px;
    border-radius: 4px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s;
}

.message-action-btn:hover {
    background: #f3f4f6;
    color: #374151;
}

.message-action-btn.liked {
    color: #10b981;
}

.message-action-btn.disliked {
    color: #ef4444;
}

.message-content p {
    margin-bottom: 16px;
}

.message-content p:last-child {
    margin-bottom: 0;
}

.message-content ul, .message-content ol {
    margin: 16px 0;
    padding-left: 24px;
}

.message-content li {
    margin-bottom: 8px;
}

.message-content code {
    background: #f3f4f6;
    padding: 2px 6px;
    border-radius: 4px;
    font-family: 'Monaco', 'Menlo', monospace;
    font-size: 14px;
}

.message-content pre {
    background: #f8f9fa;
    padding: 16px;
    border-radius: 8px;
    overflow-x: auto;
    margin: 16px 0;
}

.message-content pre code {
    background: none;
    padding: 0;
}

/* Input Section */
.input-section {
    padding: 24px;
    border-top: 1px solid #e5e7eb;
    background: white;
}

.input-container {
    max-width: 768px;
    margin: 0 auto;
    position: relative;
    background: white;
    border: 1px solid #d1d5db;
    border-radius: 12px;
    box-shadow: 0 0 0 0 rgba(0, 0, 0, 0.05);
    transition: border-color 0.2s, box-shadow 0.2s;
}

.input-container:focus-within {
    border-color: #10a37f;
    box-shadow: 0 0 0 2px rgba(16, 163, 127, 0.1);
}

#messageInput {
    width: 100%;
    border: none;
    outline: none;
    padding: 16px 50px 16px 16px;
    font-size: 16px;
    line-height: 1.5;
    resize: none;
    background: transparent;
    font-family: inherit;
    max-height: 200px;
}

#messageInput::placeholder {
    color: #9ca3af;
}

.send-button {
    position: absolute;
    right: 12px;
    top: 50%;
    transform: translateY(-50%);
    background: #10a37f;
    color: white;
    border: none;
    width: 32px;
    height: 32px;
    border-radius: 6px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: background-color 0.2s;
}

.send-button:hover:not(:disabled) {
    background: #0d8f6b;
}

.send-button:disabled {
    background: #d1d5db;
    cursor: not-allowed;
}

/* Typing indicator */
.typing-indicator {
    display: flex;
    align-items: center;
    gap: 8px;
    color: #6b7280;
    font-style: italic;
}

.typing-dots {
    display: flex;
    gap: 4px;
}

.typing-dots span {
    width: 6px;
    height: 6px;
    background: #6b7280;
    border-radius: 50%;
    animation: typing 1.4s infinite ease-in-out;
}

.typing-dots span:nth-child(1) { animation-delay: -0.32s; }
.typing-dots span:nth-child(2) { animation-delay: -0.16s; }

@keyframes typing {
    0%, 80%, 100% {
        transform: scale(0.8);
        opacity: 0.5;
    }
    40% {
        transform: scale(1);
        opacity: 1;
    }
}

/* Responsive Design */
@media (max-width: 768px) {
    .sidebar {
        display: none;
    }

    .main-content {
        width: 100%;
    }

    .messages {
        padding: 16px;
    }

    .input-section {
        padding: 16px;
    }

    .message {
        margin-bottom: 16px;
    }

    .message-avatar {
        width: 28px;
        height: 28px;
    }

    .message-content {
        font-size: 15px;
    }

    #messageInput {
        padding: 12px 40px 12px 12px;
        font-size: 16px; /* Prevents zoom on iOS */
    }
}

/* Scrollbar styling */
.chat-list::-webkit-scrollbar,
.chat-container::-webkit-scrollbar {
    width: 6px;
}

.chat-list::-webkit-scrollbar-track,
.chat-container::-webkit-scrollbar-track {
    background: transparent;
}

.chat-list::-webkit-scrollbar-thumb {
    background: #4d4d4f;
    border-radius: 3px;
}

.chat-container::-webkit-scrollbar-thumb {
    background: #d1d5db;
    border-radius: 3px;
}

.chat-list::-webkit-scrollbar-thumb:hover {
    background: #6b6b6f;
}

.chat-container::-webkit-scrollbar-thumb:hover {
    background: #9ca3af;
}

/* Context Menu */
.context-menu {
    position: fixed;
    background: white;
    border: 1px solid #e5e7eb;
    border-radius: 8px;
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
    z-index: 1000;
    min-width: 160px;
    display: none;
}

.context-menu-item {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 12px 16px;
    cursor: pointer;
    font-size: 14px;
    color: #374151;
    transition: background-color 0.2s;
}

.context-menu-item:hover {
    background: #f3f4f6;
}

.context-menu-item.delete {
    color: #ef4444;
}

.context-menu-item.delete:hover {
    background: #fef2f2;
}

/* Modal */
.modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    display: none;
    align-items: center;
    justify-content: center;
    z-index: 1001;
}

.modal.show {
    display: flex;
}

.modal-content {
    background: white;
    border-radius: 12px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2);
    max-width: 500px;
    width: 90%;
    max-height: 80vh;
    overflow-y: auto;
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px 24px;
    border-bottom: 1px solid #e5e7eb;
}

.modal-header h2 {
    margin: 0;
    font-size: 18px;
    font-weight: 600;
    color: #111827;
}

.close-btn {
    background: none;
    border: none;
    color: #6b7280;
    cursor: pointer;
    padding: 4px;
    border-radius: 4px;
    transition: all 0.2s;
}

.close-btn:hover {
    background: #f3f4f6;
    color: #374151;
}

.modal-body {
    padding: 24px;
}

.setting-group {
    margin-bottom: 20px;
}

.setting-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: 500;
    color: #374151;
}

.setting-group select,
.setting-group input[type="text"] {
    width: 100%;
    padding: 8px 12px;
    border: 1px solid #d1d5db;
    border-radius: 6px;
    font-size: 14px;
    transition: border-color 0.2s;
}

.setting-group select:focus,
.setting-group input[type="text"]:focus {
    outline: none;
    border-color: #10a37f;
    box-shadow: 0 0 0 2px rgba(16, 163, 127, 0.1);
}

.setting-group input[type="range"] {
    width: calc(100% - 40px);
    margin-right: 10px;
}

.modal-actions {
    display: flex;
    gap: 12px;
    justify-content: flex-end;
    margin-top: 24px;
}

.btn-primary,
.btn-secondary {
    padding: 8px 16px;
    border-radius: 6px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s;
}

.btn-primary {
    background: #10a37f;
    color: white;
    border: none;
}

.btn-primary:hover {
    background: #0d8f6b;
}

.btn-secondary {
    background: white;
    color: #374151;
    border: 1px solid #d1d5db;
}

.btn-secondary:hover {
    background: #f9fafb;
}

/* Profile Modal */
.profile-modal {
    max-width: 600px;
}

.profile-section {
    display: flex;
    gap: 24px;
    margin-bottom: 24px;
}

.profile-avatar-section {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 16px;
    flex-shrink: 0;
}

.profile-avatar {
    width: 120px;
    height: 120px;
    border-radius: 50%;
    background: #f3f4f6;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #6b7280;
    overflow: hidden;
    border: 4px solid #e5e7eb;
}

.profile-avatar img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.change-avatar-btn {
    background: #4f46e5;
    color: white;
    border: none;
    padding: 8px 16px;
    border-radius: 6px;
    font-size: 14px;
    cursor: pointer;
    transition: background 0.2s;
}

.change-avatar-btn:hover {
    background: #4338ca;
}

.profile-info {
    flex: 1;
}

.interests-display {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    margin-top: 8px;
}

.interest-badge {
    background: #f3f4f6;
    color: #374151;
    padding: 4px 12px;
    border-radius: 16px;
    font-size: 12px;
    font-weight: 500;
}

.profile-stats {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 16px;
    margin-top: 24px;
    padding: 16px;
    background: #f8fafc;
    border-radius: 8px;
}

.stat-item {
    text-align: center;
}

.stat-value {
    font-size: 24px;
    font-weight: 600;
    color: #1f2937;
    margin-bottom: 4px;
}

.stat-label {
    font-size: 12px;
    color: #6b7280;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

/* Dark Theme */
body.dark-theme {
    background: #1a1a1a;
    color: #e5e7eb;
}

body.dark-theme .main-content {
    background: #1a1a1a;
}

body.dark-theme .input-section {
    background: #1a1a1a;
    border-top-color: #2d2d2d;
}

body.dark-theme .input-container {
    background: #1a1a1a;
    border-color: #2d2d2d;
}

body.dark-theme .input-container:focus-within {
    border-color: #10a37f;
}

body.dark-theme #messageInput {
    color: #e5e7eb;
}

body.dark-theme #messageInput::placeholder {
    color: #6b7280;
}

body.dark-theme .user-message .message-avatar {
    background: #10a37f;
}

body.dark-theme .ai-message .message-avatar {
    background: #8b5cf6;
}

body.dark-theme .message-action-btn {
    color: #9ca3af;
}

body.dark-theme .message-action-btn:hover {
    background: #2d2d2d;
    color: #e5e7eb;
}

body.dark-theme .context-menu {
    background: #2d2d2d;
    border-color: #4d4d4f;
    color: #e5e7eb;
}

body.dark-theme .context-menu-item:hover {
    background: #3d3d3f;
}

body.dark-theme .modal-content {
    background: #2d2d2d;
    color: #e5e7eb;
}

body.dark-theme .modal-header {
    border-bottom-color: #4d4d4f;
}

body.dark-theme .setting-group label {
    color: #e5e7eb;
}

body.dark-theme .setting-group select,
body.dark-theme .setting-group input[type="text"] {
    background: #1a1a1a;
    border-color: #4d4d4f;
    color: #e5e7eb;
}

body.dark-theme .btn-secondary {
    background: #3d3d3f;
    color: #e5e7eb;
    border-color: #4d4d4f;
}

body.dark-theme .btn-secondary:hover {
    background: #4d4d4f;
}
