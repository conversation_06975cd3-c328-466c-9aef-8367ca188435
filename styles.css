* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    background: #f7f7f8;
    color: #374151;
    height: 100vh;
    overflow: hidden;
}

.app {
    display: flex;
    height: 100vh;
}

/* Sidebar */
.sidebar {
    width: 260px;
    background: #171717;
    color: white;
    display: flex;
    flex-direction: column;
    border-right: 1px solid #2d2d2d;
}

.sidebar-header {
    padding: 12px;
    border-bottom: 1px solid #2d2d2d;
}

.new-chat-btn {
    width: 100%;
    background: transparent;
    color: white;
    border: 1px solid #4d4d4f;
    padding: 12px 16px;
    border-radius: 6px;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 14px;
    transition: background-color 0.2s;
}

.new-chat-btn:hover {
    background: #2d2d2d;
}

.chat-list {
    flex: 1;
    overflow-y: auto;
    padding: 8px;
}

.chat-item {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 12px;
    border-radius: 6px;
    cursor: pointer;
    margin-bottom: 2px;
    color: #ececf1;
    font-size: 14px;
    transition: background-color 0.2s;
}

.chat-item:hover {
    background: #2d2d2d;
}

.chat-item.active {
    background: #343541;
}

.chat-item span {
    flex: 1;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

/* Main Content */
.main-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    background: white;
}

.chat-container {
    flex: 1;
    overflow-y: auto;
    padding: 0;
}

.messages {
    max-width: 768px;
    margin: 0 auto;
    padding: 24px;
}

.message {
    margin-bottom: 24px;
    display: flex;
    gap: 16px;
}

.message-avatar {
    width: 32px;
    height: 32px;
    border-radius: 4px;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
    font-size: 16px;
    font-weight: 600;
}

.user-message .message-avatar {
    background: #19c37d;
    color: white;
}

.ai-message .message-avatar {
    background: #ab68ff;
    color: white;
}

.message-content {
    flex: 1;
    line-height: 1.6;
    font-size: 16px;
}

.message-content p {
    margin-bottom: 16px;
}

.message-content p:last-child {
    margin-bottom: 0;
}

.message-content ul, .message-content ol {
    margin: 16px 0;
    padding-left: 24px;
}

.message-content li {
    margin-bottom: 8px;
}

.message-content code {
    background: #f3f4f6;
    padding: 2px 6px;
    border-radius: 4px;
    font-family: 'Monaco', 'Menlo', monospace;
    font-size: 14px;
}

.message-content pre {
    background: #f8f9fa;
    padding: 16px;
    border-radius: 8px;
    overflow-x: auto;
    margin: 16px 0;
}

.message-content pre code {
    background: none;
    padding: 0;
}

/* Input Section */
.input-section {
    padding: 24px;
    border-top: 1px solid #e5e7eb;
    background: white;
}

.input-container {
    max-width: 768px;
    margin: 0 auto;
    position: relative;
    background: white;
    border: 1px solid #d1d5db;
    border-radius: 12px;
    box-shadow: 0 0 0 0 rgba(0, 0, 0, 0.05);
    transition: border-color 0.2s, box-shadow 0.2s;
}

.input-container:focus-within {
    border-color: #10a37f;
    box-shadow: 0 0 0 2px rgba(16, 163, 127, 0.1);
}

#messageInput {
    width: 100%;
    border: none;
    outline: none;
    padding: 16px 50px 16px 16px;
    font-size: 16px;
    line-height: 1.5;
    resize: none;
    background: transparent;
    font-family: inherit;
    max-height: 200px;
}

#messageInput::placeholder {
    color: #9ca3af;
}

.send-button {
    position: absolute;
    right: 12px;
    top: 50%;
    transform: translateY(-50%);
    background: #10a37f;
    color: white;
    border: none;
    width: 32px;
    height: 32px;
    border-radius: 6px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: background-color 0.2s;
}

.send-button:hover:not(:disabled) {
    background: #0d8f6b;
}

.send-button:disabled {
    background: #d1d5db;
    cursor: not-allowed;
}

/* Typing indicator */
.typing-indicator {
    display: flex;
    align-items: center;
    gap: 8px;
    color: #6b7280;
    font-style: italic;
}

.typing-dots {
    display: flex;
    gap: 4px;
}

.typing-dots span {
    width: 6px;
    height: 6px;
    background: #6b7280;
    border-radius: 50%;
    animation: typing 1.4s infinite ease-in-out;
}

.typing-dots span:nth-child(1) { animation-delay: -0.32s; }
.typing-dots span:nth-child(2) { animation-delay: -0.16s; }

@keyframes typing {
    0%, 80%, 100% {
        transform: scale(0.8);
        opacity: 0.5;
    }
    40% {
        transform: scale(1);
        opacity: 1;
    }
}

/* Responsive Design */
@media (max-width: 768px) {
    .sidebar {
        display: none;
    }

    .main-content {
        width: 100%;
    }

    .messages {
        padding: 16px;
    }

    .input-section {
        padding: 16px;
    }

    .message {
        margin-bottom: 16px;
    }

    .message-avatar {
        width: 28px;
        height: 28px;
    }

    .message-content {
        font-size: 15px;
    }

    #messageInput {
        padding: 12px 40px 12px 12px;
        font-size: 16px; /* Prevents zoom on iOS */
    }
}

/* Scrollbar styling */
.chat-list::-webkit-scrollbar,
.chat-container::-webkit-scrollbar {
    width: 6px;
}

.chat-list::-webkit-scrollbar-track,
.chat-container::-webkit-scrollbar-track {
    background: transparent;
}

.chat-list::-webkit-scrollbar-thumb {
    background: #4d4d4f;
    border-radius: 3px;
}

.chat-container::-webkit-scrollbar-thumb {
    background: #d1d5db;
    border-radius: 3px;
}

.chat-list::-webkit-scrollbar-thumb:hover {
    background: #6b6b6f;
}

.chat-container::-webkit-scrollbar-thumb:hover {
    background: #9ca3af;
}
