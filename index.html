<!DOCTYPE html>
<html lang="ru">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ChatGPT</title>
    <link rel="stylesheet" href="styles.css">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600&display=swap" rel="stylesheet">
</head>
<body>
    <div class="app">
        <!-- Sidebar -->
        <div class="sidebar">
            <div class="sidebar-header">
                <button class="new-chat-btn" onclick="newChat()">
                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
                        <path d="M12 5v14m-7-7h14" stroke="currentColor" stroke-width="2" stroke-linecap="round"/>
                    </svg>
                    Новый чат
                </button>
            </div>

            <div class="chat-list" id="chatList">
                <div class="chat-item active" data-chat-id="1">
                    <div class="chat-content" onclick="switchToChat(1)">
                        <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
                            <path d="M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                        </svg>
                        <span>Новый чат</span>
                    </div>
                    <div class="chat-actions">
                        <button class="chat-menu-btn" onclick="showChatMenu(event, 1)">
                            <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
                                <circle cx="12" cy="12" r="1" fill="currentColor"/>
                                <circle cx="19" cy="12" r="1" fill="currentColor"/>
                                <circle cx="5" cy="12" r="1" fill="currentColor"/>
                            </svg>
                        </button>
                    </div>
                </div>
            </div>

            <div class="sidebar-footer">
                <div class="user-profile" onclick="toggleUserMenu()">
                    <div class="user-avatar-small" id="userAvatarSmall">
                        <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
                            <path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2" stroke="currentColor" stroke-width="2"/>
                            <circle cx="12" cy="7" r="4" stroke="currentColor" stroke-width="2"/>
                        </svg>
                    </div>
                    <div class="user-info">
                        <div class="user-name" id="userName">Пользователь</div>
                        <div class="user-email" id="userEmail"><EMAIL></div>
                    </div>
                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" class="chevron">
                        <polyline points="6,9 12,15 18,9" stroke="currentColor" stroke-width="2"/>
                    </svg>
                </div>

                <div class="user-menu hidden" id="userMenu">
                    <div class="user-menu-item" onclick="openProfile()">
                        <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
                            <path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2" stroke="currentColor" stroke-width="2"/>
                            <circle cx="12" cy="7" r="4" stroke="currentColor" stroke-width="2"/>
                        </svg>
                        Профиль
                    </div>
                    <div class="user-menu-item" onclick="openSettings()">
                        <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
                            <circle cx="12" cy="12" r="3" stroke="currentColor" stroke-width="2"/>
                            <path d="m12 1 1.68 4.32L18 4l-1.32 4.68L21 10l-4.32 1.68L18 16l-4.68-1.32L12 19l-1.68-4.32L6 16l1.32-4.68L3 10l4.32-1.68L6 4l4.68 1.32L12 1z" stroke="currentColor" stroke-width="2"/>
                        </svg>
                        Настройки
                    </div>
                    <div class="user-menu-divider"></div>
                    <div class="user-menu-item logout" onclick="logout()">
                        <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
                            <path d="M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4" stroke="currentColor" stroke-width="2"/>
                            <polyline points="16,17 21,12 16,7" stroke="currentColor" stroke-width="2"/>
                            <line x1="21" y1="12" x2="9" y2="12" stroke="currentColor" stroke-width="2"/>
                        </svg>
                        Выйти
                    </div>
                </div>
            </div>
        </div>

        <!-- Main Chat -->
        <div class="main-content">
            <div class="chat-container">
                <div class="messages" id="messages">
                    <!-- Messages will be added here -->
                </div>
            </div>

            <!-- Input Area -->
            <div class="input-section">
                <div class="input-container">
                    <textarea
                        id="messageInput"
                        placeholder="Отправить сообщение..."
                        rows="1"
                        onkeydown="handleKeyPress(event)"
                        oninput="adjustTextarea()"
                    ></textarea>
                    <button class="send-button" id="sendButton" onclick="sendMessage()">
                        <svg width="20" height="20" viewBox="0 0 24 24" fill="none">
                            <path d="M22 2L11 13M22 2l-7 20-4-9-9-4 20-7z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                        </svg>
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Context Menu -->
    <div class="context-menu" id="contextMenu">
        <div class="context-menu-item" onclick="renameChat()">
            <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
                <path d="M11 4H4a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7" stroke="currentColor" stroke-width="2"/>
                <path d="m18.5 2.5 3 3L12 15l-4 1 1-4 9.5-9.5z" stroke="currentColor" stroke-width="2"/>
            </svg>
            Переименовать
        </div>
        <div class="context-menu-item" onclick="showExportOptions()">
            <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
                <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4" stroke="currentColor" stroke-width="2"/>
                <polyline points="7,10 12,15 17,10" stroke="currentColor" stroke-width="2"/>
                <line x1="12" y1="15" x2="12" y2="3" stroke="currentColor" stroke-width="2"/>
            </svg>
            Экспорт чата
        </div>
        <div class="context-menu-divider"></div>
        <div class="context-menu-item delete" onclick="deleteChat()">
            <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
                <polyline points="3,6 5,6 21,6" stroke="currentColor" stroke-width="2"/>
                <path d="m19,6v14a2,2 0 0,1 -2,2H7a2,2 0 0,1 -2,-2V6m3,0V4a2,2 0 0,1 2,-2h4a2,2 0 0,1 2,2v2" stroke="currentColor" stroke-width="2"/>
            </svg>
            Удалить чат
        </div>
    </div>

    <!-- Export Options Modal -->
    <div class="modal" id="exportModal">
        <div class="modal-content">
            <div class="modal-header">
                <h2>Экспорт чата</h2>
                <button class="close-btn" onclick="closeExportModal()">
                    <svg width="20" height="20" viewBox="0 0 24 24" fill="none">
                        <line x1="18" y1="6" x2="6" y2="18" stroke="currentColor" stroke-width="2"/>
                        <line x1="6" y1="6" x2="18" y2="18" stroke="currentColor" stroke-width="2"/>
                    </svg>
                </button>
            </div>
            <div class="modal-body">
                <p>Выберите формат для экспорта чата:</p>
                <div class="export-options">
                    <button class="export-btn" onclick="exportCurrentChat('txt')">
                        <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
                            <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z" stroke="currentColor" stroke-width="2"/>
                            <polyline points="14,2 14,8 20,8" stroke="currentColor" stroke-width="2"/>
                        </svg>
                        Текстовый файл (.txt)
                    </button>
                    <button class="export-btn" onclick="exportCurrentChat('json')">
                        <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
                            <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z" stroke="currentColor" stroke-width="2"/>
                            <polyline points="14,2 14,8 20,8" stroke="currentColor" stroke-width="2"/>
                            <line x1="16" y1="13" x2="8" y2="13" stroke="currentColor" stroke-width="2"/>
                            <line x1="16" y1="17" x2="8" y2="17" stroke="currentColor" stroke-width="2"/>
                        </svg>
                        JSON файл (.json)
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Settings Modal -->
    <div class="modal" id="settingsModal">
        <div class="modal-content">
            <div class="modal-header">
                <h2>Настройки</h2>
                <button class="close-btn" onclick="closeSettings()">
                    <svg width="20" height="20" viewBox="0 0 24 24" fill="none">
                        <line x1="18" y1="6" x2="6" y2="18" stroke="currentColor" stroke-width="2"/>
                        <line x1="6" y1="6" x2="18" y2="18" stroke="currentColor" stroke-width="2"/>
                    </svg>
                </button>
            </div>
            <div class="modal-body">
                <div class="setting-group">
                    <label>Модель ИИ</label>
                    <select id="aiModel">
                        <option value="GigaChat">GigaChat</option>
                        <option value="GigaChat-Pro">GigaChat-Pro</option>
                    </select>
                    <div class="api-status" id="apiStatus">
                        <span class="status-indicator" id="statusIndicator"></span>
                        <span class="status-text" id="statusText">Проверка подключения...</span>
                    </div>
                </div>
                <div class="setting-group">
                    <label>Тема</label>
                    <select id="theme">
                        <option value="light">Светлая</option>
                        <option value="dark">Темная</option>
                    </select>
                </div>
                <div class="setting-group">
                    <label>Температура (креативность)</label>
                    <input type="range" id="temperature" min="0" max="1" step="0.1" value="0.7">
                    <span id="temperatureValue">0.7</span>
                </div>
            </div>
        </div>
    </div>

    <!-- Rename Modal -->
    <div class="modal" id="renameModal">
        <div class="modal-content">
            <div class="modal-header">
                <h2>Переименовать чат</h2>
                <button class="close-btn" onclick="closeRename()">
                    <svg width="20" height="20" viewBox="0 0 24 24" fill="none">
                        <line x1="18" y1="6" x2="6" y2="18" stroke="currentColor" stroke-width="2"/>
                        <line x1="6" y1="6" x2="18" y2="18" stroke="currentColor" stroke-width="2"/>
                    </svg>
                </button>
            </div>
            <div class="modal-body">
                <input type="text" id="chatNameInput" placeholder="Введите новое название чата">
                <div class="modal-actions">
                    <button class="btn-secondary" onclick="closeRename()">Отмена</button>
                    <button class="btn-primary" onclick="confirmRename()">Сохранить</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Profile Modal -->
    <div class="modal" id="profileModal">
        <div class="modal-content profile-modal">
            <div class="modal-header">
                <h2>Профиль пользователя</h2>
                <button class="close-btn" onclick="closeProfile()">
                    <svg width="20" height="20" viewBox="0 0 24 24" fill="none">
                        <line x1="18" y1="6" x2="6" y2="18" stroke="currentColor" stroke-width="2"/>
                        <line x1="6" y1="6" x2="18" y2="18" stroke="currentColor" stroke-width="2"/>
                    </svg>
                </button>
            </div>
            <div class="modal-body">
                <div class="profile-section">
                    <div class="profile-avatar-section">
                        <div class="profile-avatar" id="profileAvatar">
                            <svg width="60" height="60" viewBox="0 0 24 24" fill="none">
                                <path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2" stroke="currentColor" stroke-width="2"/>
                                <circle cx="12" cy="7" r="4" stroke="currentColor" stroke-width="2"/>
                            </svg>
                        </div>
                        <button class="change-avatar-btn" onclick="changeAvatar()">Изменить фото</button>
                        <input type="file" id="profileAvatarInput" accept="image/*" style="display: none;" onchange="updateProfileAvatar(event)">
                    </div>

                    <div class="profile-info">
                        <div class="form-group">
                            <label for="profileDisplayName">Отображаемое имя</label>
                            <input type="text" id="profileDisplayName" placeholder="Ваше имя">
                        </div>

                        <div class="form-group">
                            <label for="profileEmail">Email</label>
                            <input type="email" id="profileEmail" readonly>
                        </div>

                        <div class="form-group">
                            <label for="profileBio">О себе</label>
                            <textarea id="profileBio" placeholder="Расскажите о себе..." rows="3"></textarea>
                        </div>

                        <div class="form-group">
                            <label>Интересы</label>
                            <div class="interests-display" id="profileInterests">
                                <!-- Interests will be populated here -->
                            </div>
                        </div>

                        <div class="profile-stats">
                            <div class="stat-item">
                                <div class="stat-value" id="totalChats">0</div>
                                <div class="stat-label">Чатов</div>
                            </div>
                            <div class="stat-item">
                                <div class="stat-value" id="totalMessages">0</div>
                                <div class="stat-label">Сообщений</div>
                            </div>
                            <div class="stat-item">
                                <div class="stat-value" id="memberSince">-</div>
                                <div class="stat-label">Участник с</div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="modal-actions">
                    <button class="btn-secondary" onclick="closeProfile()">Отмена</button>
                    <button class="btn-primary" onclick="saveProfile()">Сохранить изменения</button>
                </div>
            </div>
        </div>
    </div>

    <script src="simple-ai.js"></script>
    <script src="script.js"></script>
</body>
</html>
