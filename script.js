// DOM Elements
const messageInput = document.getElementById('messageInput');
const sendBtn = document.getElementById('sendBtn');
const chatMessages = document.getElementById('chatMessages');
const loadingOverlay = document.getElementById('loadingOverlay');
const modelSelect = document.getElementById('modelSelect');

// State
let isTyping = false;

// Initialize
document.addEventListener('DOMContentLoaded', function() {
    adjustTextareaHeight();
    messageInput.focus();
});

// Auto-resize textarea
function adjustTextareaHeight() {
    messageInput.style.height = 'auto';
    messageInput.style.height = messageInput.scrollHeight + 'px';
}

messageInput.addEventListener('input', adjustTextareaHeight);

// Handle keyboard shortcuts
function handleKeyDown(event) {
    if (event.key === 'Enter' && !event.shiftKey) {
        event.preventDefault();
        sendMessage();
    }
}

// Send message function
async function sendMessage() {
    const message = messageInput.value.trim();
    if (!message || isTyping) return;

    // Add user message
    addMessage(message, 'user');
    messageInput.value = '';
    adjustTextareaHeight();

    // Show typing indicator
    showTypingIndicator();

    // Simulate AI response
    setTimeout(() => {
        hideTypingIndicator();
        const responses = [
            "Отличный вопрос! Я помогу вам разобраться с этим. Вот что я могу предложить...",
            "Понимаю вашу задачу. Давайте решим это пошагово:",
            "Это интересная тема! Позвольте мне объяснить подробнее...",
            "Я проанализировал ваш запрос. Вот мой ответ:",
            "Хороший вопрос! Вот что я думаю по этому поводу..."
        ];
        const randomResponse = responses[Math.floor(Math.random() * responses.length)];
        addMessage(randomResponse, 'ai');
    }, 1500 + Math.random() * 2000);
}

// Send quick message
function sendQuickMessage(message) {
    messageInput.value = message;
    sendMessage();
}

// Add message to chat
function addMessage(text, sender) {
    const messageDiv = document.createElement('div');
    messageDiv.className = `message ${sender}-message`;
    
    const avatarDiv = document.createElement('div');
    avatarDiv.className = 'message-avatar';
    avatarDiv.innerHTML = sender === 'ai' ? '<i class="fas fa-robot"></i>' : '<i class="fas fa-user"></i>';
    
    const contentDiv = document.createElement('div');
    contentDiv.className = 'message-content';
    
    const textDiv = document.createElement('div');
    textDiv.className = 'message-text';
    textDiv.textContent = text;
    
    const timeDiv = document.createElement('div');
    timeDiv.className = 'message-time';
    timeDiv.textContent = new Date().toLocaleTimeString('ru-RU', { 
        hour: '2-digit', 
        minute: '2-digit' 
    });
    
    contentDiv.appendChild(textDiv);
    contentDiv.appendChild(timeDiv);
    messageDiv.appendChild(avatarDiv);
    messageDiv.appendChild(contentDiv);
    
    chatMessages.appendChild(messageDiv);
    chatMessages.scrollTop = chatMessages.scrollHeight;
}

// Typing indicator
function showTypingIndicator() {
    isTyping = true;
    sendBtn.disabled = true;
    
    const typingDiv = document.createElement('div');
    typingDiv.className = 'message ai-message typing-indicator';
    typingDiv.id = 'typingIndicator';
    
    typingDiv.innerHTML = `
        <div class="message-avatar">
            <i class="fas fa-robot"></i>
        </div>
        <div class="message-content">
            <div class="message-text">
                <div class="typing-dots">
                    <span></span>
                    <span></span>
                    <span></span>
                </div>
            </div>
        </div>
    `;
    
    chatMessages.appendChild(typingDiv);
    chatMessages.scrollTop = chatMessages.scrollHeight;
}

function hideTypingIndicator() {
    isTyping = false;
    sendBtn.disabled = false;
    const typingIndicator = document.getElementById('typingIndicator');
    if (typingIndicator) {
        typingIndicator.remove();
    }
}

// Sidebar functionality
document.addEventListener('click', function(e) {
    // New chat button
    if (e.target.closest('.new-chat-btn')) {
        startNewChat();
    }
    
    // Chat item selection
    if (e.target.closest('.chat-item')) {
        const chatItems = document.querySelectorAll('.chat-item');
        chatItems.forEach(item => item.classList.remove('active'));
        e.target.closest('.chat-item').classList.add('active');
    }
    
    // Chat options
    if (e.target.closest('.chat-options')) {
        e.stopPropagation();
        showChatOptions(e.target.closest('.chat-options'));
    }
});

function startNewChat() {
    // Clear messages except welcome message
    const messages = chatMessages.querySelectorAll('.message');
    messages.forEach((message, index) => {
        if (index > 0) { // Keep first welcome message
            message.remove();
        }
    });
    
    // Create new chat item
    const chatHistory = document.querySelector('.chat-history');
    const newChatItem = document.createElement('div');
    newChatItem.className = 'chat-item active';
    newChatItem.innerHTML = `
        <i class="fas fa-message"></i>
        <span>Новый чат</span>
        <button class="chat-options">
            <i class="fas fa-ellipsis-h"></i>
        </button>
    `;
    
    // Remove active class from other items
    document.querySelectorAll('.chat-item').forEach(item => {
        item.classList.remove('active');
    });
    
    chatHistory.insertBefore(newChatItem, chatHistory.firstChild);
    messageInput.focus();
}

function showChatOptions(button) {
    // Simple implementation - could be expanded with a proper context menu
    const options = ['Переименовать', 'Удалить', 'Экспорт'];
    const choice = prompt('Выберите действие:\n1. Переименовать\n2. Удалить\n3. Экспорт');
    
    switch(choice) {
        case '1':
            const newName = prompt('Введите новое название:');
            if (newName) {
                const chatItem = button.closest('.chat-item');
                const nameSpan = chatItem.querySelector('span');
                nameSpan.textContent = newName;
            }
            break;
        case '2':
            if (confirm('Удалить этот чат?')) {
                button.closest('.chat-item').remove();
            }
            break;
        case '3':
            alert('Функция экспорта будет добавлена в следующей версии');
            break;
    }
}

// Model selection
modelSelect.addEventListener('change', function() {
    const selectedModel = this.value;
    console.log('Selected model:', selectedModel);
    // Here you would typically update the AI model being used
});

// Smooth scrolling for chat
function smoothScrollToBottom() {
    chatMessages.scrollTo({
        top: chatMessages.scrollHeight,
        behavior: 'smooth'
    });
}

// File attachment (placeholder)
document.querySelector('.attach-btn').addEventListener('click', function() {
    const input = document.createElement('input');
    input.type = 'file';
    input.multiple = true;
    input.accept = '.txt,.pdf,.doc,.docx,.jpg,.png,.gif';
    
    input.onchange = function(e) {
        const files = Array.from(e.target.files);
        files.forEach(file => {
            addMessage(`📎 Файл прикреплен: ${file.name}`, 'user');
        });
    };
    
    input.click();
});

// Responsive sidebar toggle
function toggleSidebar() {
    const sidebar = document.querySelector('.sidebar');
    sidebar.classList.toggle('mobile-hidden');
}

// Add mobile menu button functionality
if (window.innerWidth <= 768) {
    const header = document.querySelector('.header-content');
    const menuBtn = document.createElement('button');
    menuBtn.className = 'mobile-menu-btn';
    menuBtn.innerHTML = '<i class="fas fa-bars"></i>';
    menuBtn.onclick = toggleSidebar;
    header.insertBefore(menuBtn, header.firstChild);
}

// Theme switching (bonus feature)
function toggleTheme() {
    document.body.classList.toggle('dark-theme');
    localStorage.setItem('theme', document.body.classList.contains('dark-theme') ? 'dark' : 'light');
}

// Load saved theme
if (localStorage.getItem('theme') === 'dark') {
    document.body.classList.add('dark-theme');
}

// Add some sample interactions for demo
setTimeout(() => {
    if (chatMessages.children.length === 1) { // Only welcome message
        addMessage("Привет! Как дела?", 'user');
        setTimeout(() => {
            addMessage("Привет! У меня всё отлично, спасибо! Готов помочь вам с любыми вопросами. Что вас интересует?", 'ai');
        }, 1000);
    }
}, 3000);

// Keyboard shortcuts
document.addEventListener('keydown', function(e) {
    // Ctrl/Cmd + K for new chat
    if ((e.ctrlKey || e.metaKey) && e.key === 'k') {
        e.preventDefault();
        startNewChat();
    }
    
    // Escape to focus input
    if (e.key === 'Escape') {
        messageInput.focus();
    }
});

// Add typing dots animation CSS
const style = document.createElement('style');
style.textContent = `
    .typing-dots {
        display: flex;
        gap: 4px;
        align-items: center;
    }
    
    .typing-dots span {
        width: 8px;
        height: 8px;
        border-radius: 50%;
        background: #64748b;
        animation: typing 1.4s infinite ease-in-out;
    }
    
    .typing-dots span:nth-child(1) { animation-delay: -0.32s; }
    .typing-dots span:nth-child(2) { animation-delay: -0.16s; }
    
    @keyframes typing {
        0%, 80%, 100% {
            transform: scale(0.8);
            opacity: 0.5;
        }
        40% {
            transform: scale(1);
            opacity: 1;
        }
    }
    
    .mobile-menu-btn {
        background: none;
        border: none;
        color: white;
        font-size: 1.2rem;
        cursor: pointer;
        padding: 0.5rem;
        border-radius: 8px;
        transition: background 0.3s ease;
    }
    
    .mobile-menu-btn:hover {
        background: rgba(255, 255, 255, 0.2);
    }
    
    @media (max-width: 768px) {
        .sidebar.mobile-hidden {
            transform: translateX(-100%);
        }
        
        .sidebar {
            position: fixed;
            top: 0;
            left: 0;
            height: 100vh;
            z-index: 100;
            transition: transform 0.3s ease;
        }
    }
`;
document.head.appendChild(style);
