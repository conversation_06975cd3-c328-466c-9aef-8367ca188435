* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Robot<PERSON>, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 20px;
}

.auth-container {
    width: 100%;
    max-width: 400px;
}

.auth-card {
    background: white;
    border-radius: 16px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    overflow: hidden;
}

.auth-header {
    text-align: center;
    padding: 40px 40px 20px;
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
}

.logo {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 12px;
    margin-bottom: 16px;
}

.logo svg {
    color: #4f46e5;
}

.logo h1 {
    font-size: 28px;
    font-weight: 700;
    color: #1f2937;
}

.auth-subtitle {
    color: #6b7280;
    font-size: 16px;
}

.auth-form {
    padding: 40px;
}

.auth-form.hidden {
    display: none;
}

.form-group {
    margin-bottom: 24px;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: 500;
    color: #374151;
    font-size: 14px;
}

.form-group input,
.form-group textarea {
    width: 100%;
    padding: 12px 16px;
    border: 1px solid #d1d5db;
    border-radius: 8px;
    font-size: 16px;
    transition: all 0.2s;
    font-family: inherit;
}

.form-group input:focus,
.form-group textarea:focus {
    outline: none;
    border-color: #4f46e5;
    box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.1);
}

.form-group textarea {
    resize: vertical;
    min-height: 80px;
}

.password-hint {
    font-size: 12px;
    color: #6b7280;
    margin-top: 4px;
}

.form-options {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 24px;
    font-size: 14px;
}

.checkbox-label {
    display: flex;
    align-items: center;
    cursor: pointer;
    color: #374151;
}

.checkbox-label input[type="checkbox"] {
    display: none;
}

.checkmark {
    width: 18px;
    height: 18px;
    border: 2px solid #d1d5db;
    border-radius: 4px;
    margin-right: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s;
}

.checkbox-label input[type="checkbox"]:checked + .checkmark {
    background: #4f46e5;
    border-color: #4f46e5;
}

.checkbox-label input[type="checkbox"]:checked + .checkmark::after {
    content: '✓';
    color: white;
    font-size: 12px;
    font-weight: bold;
}

.forgot-link {
    color: #4f46e5;
    text-decoration: none;
    font-size: 14px;
}

.forgot-link:hover {
    text-decoration: underline;
}

.auth-btn {
    width: 100%;
    padding: 12px 24px;
    border-radius: 8px;
    font-size: 16px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s;
    border: none;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    margin-bottom: 16px;
}

.auth-btn.primary {
    background: #4f46e5;
    color: white;
}

.auth-btn.primary:hover {
    background: #4338ca;
}

.auth-btn.secondary {
    background: #f3f4f6;
    color: #374151;
    border: 1px solid #d1d5db;
}

.auth-btn.secondary:hover {
    background: #e5e7eb;
}

.auth-btn.google {
    background: white;
    color: #374151;
    border: 1px solid #d1d5db;
}

.auth-btn.google:hover {
    background: #f9fafb;
}

.auth-divider {
    text-align: center;
    margin: 24px 0;
    position: relative;
    color: #6b7280;
    font-size: 14px;
}

.auth-divider::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 0;
    right: 0;
    height: 1px;
    background: #e5e7eb;
}

.auth-divider span {
    background: white;
    padding: 0 16px;
    position: relative;
}

.auth-switch {
    text-align: center;
    margin-top: 24px;
    font-size: 14px;
    color: #6b7280;
}

.auth-switch a {
    color: #4f46e5;
    text-decoration: none;
    font-weight: 500;
}

.auth-switch a:hover {
    text-decoration: underline;
}

/* Profile Setup Styles */
.profile-header {
    text-align: center;
    margin-bottom: 32px;
}

.profile-header h2 {
    font-size: 24px;
    font-weight: 600;
    color: #1f2937;
    margin-bottom: 8px;
}

.profile-header p {
    color: #6b7280;
    font-size: 16px;
}

.avatar-upload {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 16px;
}

.avatar-preview {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    background: #f3f4f6;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #6b7280;
    overflow: hidden;
}

.avatar-preview img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.upload-btn {
    background: #4f46e5;
    color: white;
    border: none;
    padding: 8px 16px;
    border-radius: 6px;
    font-size: 14px;
    cursor: pointer;
    transition: background 0.2s;
}

.upload-btn:hover {
    background: #4338ca;
}

#avatarInput {
    display: none;
}

.interests-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: 12px;
    margin-top: 8px;
}

.interest-tag {
    display: flex;
    align-items: center;
    padding: 8px 12px;
    border: 1px solid #d1d5db;
    border-radius: 20px;
    cursor: pointer;
    transition: all 0.2s;
    font-size: 14px;
    justify-content: center;
}

.interest-tag input[type="checkbox"] {
    display: none;
}

.interest-tag:hover {
    border-color: #4f46e5;
    background: #f8fafc;
}

.interest-tag input[type="checkbox"]:checked + span {
    color: white;
}

.interest-tag:has(input[type="checkbox"]:checked) {
    background: #4f46e5;
    border-color: #4f46e5;
    color: white;
}

/* Responsive Design */
@media (max-width: 480px) {
    .auth-container {
        padding: 0;
    }
    
    .auth-card {
        border-radius: 0;
        min-height: 100vh;
    }
    
    .auth-header,
    .auth-form {
        padding: 24px;
    }
    
    .interests-grid {
        grid-template-columns: 1fr 1fr;
    }
}

/* Loading State */
.auth-btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
}

.auth-btn.loading::after {
    content: '';
    width: 16px;
    height: 16px;
    border: 2px solid transparent;
    border-top: 2px solid currentColor;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-left: 8px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}
