// GigaChat API Integration
class GigaChatAPI {
    constructor() {
        this.apiKey = 'ZWMyMzlkZjUtYzVhOS00OTExLThhMGUtMGMwNmNiMmZhNDg4OmYzNTgwMGQwLTIzNjYtNGI1OC05ZGI5LTBkOWI1NzNjMmE1Nw==';
        this.oauthURL = 'https://ngw.devices.sberbank.ru:9443/api/v2/oauth';
        this.baseURL = 'https://gigachat.devices.sberbank.ru/api/v1';
        this.accessToken = null;
        this.tokenExpiry = null;
        this.corsProxy = 'https://cors-anywhere.herokuapp.com/'; // Публичный CORS прокси
    }

    // Получение токена доступа
    async getAccessToken() {
        try {
            // Пробуем прямое подключение
            let response;
            try {
                response = await fetch(this.oauthURL, {
                    method: 'POST',
                    mode: 'cors',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                        'Accept': 'application/json',
                        'RqUID': this.generateUUID(),
                        'Authorization': `Basic ${this.apiKey}`
                    },
                    body: 'scope=GIGACHAT_API_PERS'
                });
            } catch (corsError) {
                console.log('CORS error, trying with public proxy...');
                // Если CORS ошибка, пробуем через публичный прокси
                response = await fetch(`${this.corsProxy}${this.oauthURL}`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                        'Accept': 'application/json',
                        'RqUID': this.generateUUID(),
                        'Authorization': `Basic ${this.apiKey}`,
                        'X-Requested-With': 'XMLHttpRequest'
                    },
                    body: 'scope=GIGACHAT_API_PERS'
                });
            }

            if (!response.ok) {
                const errorText = await response.text();
                throw new Error(`HTTP error! status: ${response.status}, message: ${errorText}`);
            }

            const data = await response.json();
            this.accessToken = data.access_token;
            this.tokenExpiry = Date.now() + (data.expires_in * 1000);

            console.log('GigaChat token obtained successfully');
            return this.accessToken;
        } catch (error) {
            console.error('Error getting access token:', error);
            throw error;
        }
    }

    // Проверка и обновление токена
    async ensureValidToken() {
        if (!this.accessToken || Date.now() >= this.tokenExpiry - 60000) {
            await this.getAccessToken();
        }
        return this.accessToken;
    }

    // Отправка сообщения в GigaChat
    async sendMessage(messages, options = {}) {
        try {
            await this.ensureValidToken();

            const requestBody = {
                model: options.model || 'GigaChat',
                messages: messages,
                temperature: options.temperature || 0.7,
                max_tokens: options.maxTokens || 2048,
                stream: false
            };

            // Пробуем прямое подключение
            let response;
            try {
                response = await fetch(`${this.baseURL}/chat/completions`, {
                    method: 'POST',
                    mode: 'cors',
                    headers: {
                        'Content-Type': 'application/json',
                        'Accept': 'application/json',
                        'Authorization': `Bearer ${this.accessToken}`
                    },
                    body: JSON.stringify(requestBody)
                });
            } catch (corsError) {
                console.log('CORS error, trying with public proxy...');
                // Если CORS ошибка, пробуем через публичный прокси
                response = await fetch(`${this.corsProxy}${this.baseURL}/chat/completions`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Accept': 'application/json',
                        'Authorization': `Bearer ${this.accessToken}`,
                        'X-Requested-With': 'XMLHttpRequest'
                    },
                    body: JSON.stringify(requestBody)
                });
            }

            if (!response.ok) {
                const errorData = await response.text();
                throw new Error(`GigaChat API error: ${response.status} - ${errorData}`);
            }

            const data = await response.json();

            if (data.choices && data.choices.length > 0) {
                return data.choices[0].message.content;
            } else {
                throw new Error('No response from GigaChat');
            }
        } catch (error) {
            console.error('Error sending message to GigaChat:', error);
            throw error;
        }
    }

    // Получение списка доступных моделей
    async getModels() {
        try {
            await this.ensureValidToken();

            // Пробуем прямое подключение
            let response;
            try {
                response = await fetch(`${this.baseURL}/models`, {
                    method: 'GET',
                    mode: 'cors',
                    headers: {
                        'Accept': 'application/json',
                        'Authorization': `Bearer ${this.accessToken}`
                    }
                });
            } catch (corsError) {
                console.log('CORS error, trying with public proxy...');
                // Если CORS ошибка, пробуем через публичный прокси
                response = await fetch(`${this.corsProxy}${this.baseURL}/models`, {
                    method: 'GET',
                    headers: {
                        'Accept': 'application/json',
                        'Authorization': `Bearer ${this.accessToken}`,
                        'X-Requested-With': 'XMLHttpRequest'
                    }
                });
            }

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            const data = await response.json();
            return data.data || data;
        } catch (error) {
            console.error('Error getting models:', error);
            return [
                { id: 'GigaChat', object: 'model' },
                { id: 'GigaChat-Pro', object: 'model' }
            ]; // Fallback
        }
    }

    // Генерация UUID для запросов
    generateUUID() {
        return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
            const r = Math.random() * 16 | 0;
            const v = c == 'x' ? r : (r & 0x3 | 0x8);
            return v.toString(16);
        });
    }

    // Проверка статуса API
    async checkStatus() {
        try {
            await this.ensureValidToken();
            return { status: 'connected', token: !!this.accessToken };
        } catch (error) {
            return { status: 'error', error: error.message };
        }
    }
}

// Создание глобального экземпляра API
window.gigaChatAPI = new GigaChatAPI();

// Функция для отправки сообщения с обработкой ошибок
async function sendMessageToGigaChat(userMessage, chatHistory = []) {
    try {
        // Проверяем доступность API
        if (!window.gigaChatAPI) {
            throw new Error('GigaChat API не инициализирован');
        }

        // Подготовка истории сообщений для API
        const messages = [
            {
                role: 'system',
                content: 'Ты полезный AI-ассистент. Отвечай на русском языке, будь дружелюбным и информативным. Давай развернутые и полезные ответы.'
            }
        ];

        // Добавляем последние сообщения из истории (максимум 10 для контекста)
        const recentHistory = chatHistory.slice(-10);
        recentHistory.forEach(msg => {
            if (msg.text && msg.sender) {
                messages.push({
                    role: msg.sender === 'user' ? 'user' : 'assistant',
                    content: msg.text
                });
            }
        });

        // Добавляем текущее сообщение пользователя
        messages.push({
            role: 'user',
            content: userMessage
        });

        // Получаем настройки пользователя
        const settings = getUserSettings();

        // Отправляем запрос к GigaChat
        const response = await window.gigaChatAPI.sendMessage(messages, {
            model: settings.aiModel || 'GigaChat',
            temperature: parseFloat(settings.temperature) || 0.7,
            maxTokens: 2048
        });

        return response;
    } catch (error) {
        console.error('GigaChat API Error:', error);

        // Fallback к демо-ответам если API недоступен
        if (error.message.includes('Failed to fetch') ||
            error.message.includes('CORS') ||
            error.message.includes('не инициализирован')) {

            return getFallbackResponse(userMessage);
        }

        // Возвращаем сообщение об ошибке
        if (error.message.includes('401') || error.message.includes('403')) {
            return 'Ошибка авторизации GigaChat. Проверьте API ключ или запустите прокси-сервер.';
        } else if (error.message.includes('429')) {
            return 'Превышен лимит запросов GigaChat. Попробуйте позже.';
        } else if (error.message.includes('network') || error.message.includes('fetch')) {
            return 'Ошибка сети. Для работы с GigaChat запустите прокси-сервер командой: node proxy-server.js';
        } else {
            return `Ошибка GigaChat: ${error.message}. Используется демо-режим.`;
        }
    }
}

// Fallback ответы когда API недоступен
function getFallbackResponse(userMessage) {
    const responses = [
        `Понял ваш вопрос: "${userMessage}". К сожалению, GigaChat API временно недоступен. Для полноценной работы запустите прокси-сервер.`,
        `Интересный вопрос! Сейчас работаю в демо-режиме, так как GigaChat API недоступен. Запустите proxy-server.js для полной функциональности.`,
        `Спасибо за сообщение! В данный момент используется демо-режим. Для подключения к GigaChat запустите прокси-сервер.`,
        `Ваш запрос получен, но GigaChat API недоступен. Проверьте подключение или запустите локальный прокси-сервер.`
    ];

    return responses[Math.floor(Math.random() * responses.length)];
}

// Функция для получения настроек пользователя
function getUserSettings() {
    return {
        aiModel: localStorage.getItem('aiModel') || 'GigaChat',
        temperature: localStorage.getItem('temperature') || '0.7'
    };
}

// Функция для тестирования подключения
async function testGigaChatConnection() {
    try {
        showNotification('Проверка подключения к GigaChat...', 'info');
        
        const status = await window.gigaChatAPI.checkStatus();
        
        if (status.status === 'connected') {
            showNotification('Подключение к GigaChat успешно!', 'success');
            return true;
        } else {
            showNotification(`Ошибка подключения: ${status.error}`, 'error');
            return false;
        }
    } catch (error) {
        showNotification('Не удалось подключиться к GigaChat', 'error');
        return false;
    }
}

// Функция для получения доступных моделей
async function loadAvailableModels() {
    try {
        const models = await window.gigaChatAPI.getModels();
        const modelSelect = document.getElementById('aiModel');
        
        if (modelSelect && models.length > 0) {
            // Очищаем текущие опции
            modelSelect.innerHTML = '';
            
            // Добавляем доступные модели
            models.forEach(model => {
                const option = document.createElement('option');
                option.value = model.id;
                option.textContent = model.id;
                modelSelect.appendChild(option);
            });
            
            // Восстанавливаем сохраненную модель
            const savedModel = localStorage.getItem('aiModel');
            if (savedModel) {
                modelSelect.value = savedModel;
            }
        }
    } catch (error) {
        console.error('Error loading models:', error);
    }
}

// Инициализация API при загрузке страницы
document.addEventListener('DOMContentLoaded', function() {
    // Загружаем доступные модели
    setTimeout(loadAvailableModels, 1000);
    
    // Добавляем кнопку тестирования в настройки
    const settingsModal = document.getElementById('settingsModal');
    if (settingsModal) {
        const modalBody = settingsModal.querySelector('.modal-body');
        if (modalBody) {
            const testButton = document.createElement('button');
            testButton.className = 'btn-secondary';
            testButton.textContent = 'Тестировать подключение';
            testButton.onclick = testGigaChatConnection;
            testButton.style.marginTop = '16px';
            testButton.style.width = '100%';
            
            modalBody.appendChild(testButton);
        }
    }
});

// Экспорт для использования в других файлах
if (typeof module !== 'undefined' && module.exports) {
    module.exports = { GigaChatAPI, sendMessageToGigaChat };
}
