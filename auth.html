<!DOCTYPE html>
<html lang="ru">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Вход в ChatGPT</title>
    <link rel="stylesheet" href="auth.css">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600&display=swap" rel="stylesheet">
</head>
<body>
    <div class="auth-container">
        <div class="auth-card">
            <div class="auth-header">
                <div class="logo">
                    <svg width="32" height="32" viewBox="0 0 24 24" fill="none">
                        <path d="M12 2L2 7l10 5 10-5-10-5zM2 17l10 5 10-5M2 12l10 5 10-5" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                    </svg>
                    <h1>ChatGPT</h1>
                </div>
                <p class="auth-subtitle">Добро пожаловать обратно</p>
            </div>

            <!-- Login Form -->
            <div class="auth-form" id="loginForm">
                <form onsubmit="handleLogin(event)">
                    <div class="form-group">
                        <label for="loginEmail">Email адрес</label>
                        <input type="email" id="loginEmail" required placeholder="Введите ваш email">
                    </div>
                    
                    <div class="form-group">
                        <label for="loginPassword">Пароль</label>
                        <input type="password" id="loginPassword" required placeholder="Введите пароль">
                    </div>
                    
                    <div class="form-options">
                        <label class="checkbox-label">
                            <input type="checkbox" id="rememberMe">
                            <span class="checkmark"></span>
                            Запомнить меня
                        </label>
                        <a href="#" class="forgot-link">Забыли пароль?</a>
                    </div>
                    
                    <button type="submit" class="auth-btn primary">Войти</button>
                </form>
                
                <div class="auth-divider">
                    <span>или</span>
                </div>
                
                <button class="auth-btn google" onclick="loginWithGoogle()">
                    <svg width="20" height="20" viewBox="0 0 24 24">
                        <path fill="#4285F4" d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"/>
                        <path fill="#34A853" d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"/>
                        <path fill="#FBBC05" d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"/>
                        <path fill="#EA4335" d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"/>
                    </svg>
                    Продолжить с Google
                </button>
                
                <div class="auth-switch">
                    <p>Нет аккаунта? <a href="#" onclick="showRegister()">Зарегистрироваться</a></p>
                </div>
            </div>

            <!-- Register Form -->
            <div class="auth-form hidden" id="registerForm">
                <form onsubmit="handleRegister(event)">
                    <div class="form-group">
                        <label for="registerName">Полное имя</label>
                        <input type="text" id="registerName" required placeholder="Введите ваше имя">
                    </div>
                    
                    <div class="form-group">
                        <label for="registerEmail">Email адрес</label>
                        <input type="email" id="registerEmail" required placeholder="Введите ваш email">
                    </div>
                    
                    <div class="form-group">
                        <label for="registerPassword">Пароль</label>
                        <input type="password" id="registerPassword" required placeholder="Создайте пароль" minlength="8">
                        <div class="password-hint">Минимум 8 символов</div>
                    </div>
                    
                    <div class="form-group">
                        <label for="confirmPassword">Подтвердите пароль</label>
                        <input type="password" id="confirmPassword" required placeholder="Повторите пароль">
                    </div>
                    
                    <div class="form-options">
                        <label class="checkbox-label">
                            <input type="checkbox" id="agreeTerms" required>
                            <span class="checkmark"></span>
                            Я согласен с <a href="#">Условиями использования</a> и <a href="#">Политикой конфиденциальности</a>
                        </label>
                    </div>
                    
                    <button type="submit" class="auth-btn primary">Создать аккаунт</button>
                </form>
                
                <div class="auth-divider">
                    <span>или</span>
                </div>
                
                <button class="auth-btn google" onclick="loginWithGoogle()">
                    <svg width="20" height="20" viewBox="0 0 24 24">
                        <path fill="#4285F4" d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"/>
                        <path fill="#34A853" d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"/>
                        <path fill="#FBBC05" d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"/>
                        <path fill="#EA4335" d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"/>
                    </svg>
                    Продолжить с Google
                </button>
                
                <div class="auth-switch">
                    <p>Уже есть аккаунт? <a href="#" onclick="showLogin()">Войти</a></p>
                </div>
            </div>

            <!-- Profile Setup Form -->
            <div class="auth-form hidden" id="profileForm">
                <div class="profile-header">
                    <h2>Настройка профиля</h2>
                    <p>Расскажите нам немного о себе</p>
                </div>
                
                <form onsubmit="handleProfileSetup(event)">
                    <div class="form-group">
                        <label>Фото профиля</label>
                        <div class="avatar-upload">
                            <div class="avatar-preview" id="avatarPreview">
                                <svg width="40" height="40" viewBox="0 0 24 24" fill="none">
                                    <path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2" stroke="currentColor" stroke-width="2"/>
                                    <circle cx="12" cy="7" r="4" stroke="currentColor" stroke-width="2"/>
                                </svg>
                            </div>
                            <input type="file" id="avatarInput" accept="image/*" onchange="previewAvatar(event)">
                            <button type="button" class="upload-btn" onclick="document.getElementById('avatarInput').click()">
                                Загрузить фото
                            </button>
                        </div>
                    </div>
                    
                    <div class="form-group">
                        <label for="displayName">Отображаемое имя</label>
                        <input type="text" id="displayName" required placeholder="Как вас называть?">
                    </div>
                    
                    <div class="form-group">
                        <label for="bio">О себе (необязательно)</label>
                        <textarea id="bio" placeholder="Расскажите немного о себе..." rows="3"></textarea>
                    </div>
                    
                    <div class="form-group">
                        <label for="interests">Интересы</label>
                        <div class="interests-grid">
                            <label class="interest-tag">
                                <input type="checkbox" value="programming">
                                <span>Программирование</span>
                            </label>
                            <label class="interest-tag">
                                <input type="checkbox" value="design">
                                <span>Дизайн</span>
                            </label>
                            <label class="interest-tag">
                                <input type="checkbox" value="writing">
                                <span>Писательство</span>
                            </label>
                            <label class="interest-tag">
                                <input type="checkbox" value="business">
                                <span>Бизнес</span>
                            </label>
                            <label class="interest-tag">
                                <input type="checkbox" value="education">
                                <span>Образование</span>
                            </label>
                            <label class="interest-tag">
                                <input type="checkbox" value="science">
                                <span>Наука</span>
                            </label>
                        </div>
                    </div>
                    
                    <button type="submit" class="auth-btn primary">Завершить настройку</button>
                    <button type="button" class="auth-btn secondary" onclick="skipProfile()">Пропустить</button>
                </form>
            </div>
        </div>
    </div>

    <script src="auth.js"></script>
</body>
</html>
