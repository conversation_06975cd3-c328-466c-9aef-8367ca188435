// DOM Elements
const messageInput = document.getElementById('messageInput');
const sendButton = document.getElementById('sendButton');
const messages = document.getElementById('messages');
const chatList = document.getElementById('chatList');

// State
let isTyping = false;
let currentChatId = 1;
let chats = {
    1: {
        id: 1,
        title: 'Новый чат',
        messages: []
    }
};

// Initialize
document.addEventListener('DOMContentLoaded', function() {
    messageInput.focus();
    updateSendButton();
});

// Auto-resize textarea
function adjustTextarea() {
    messageInput.style.height = 'auto';
    messageInput.style.height = Math.min(messageInput.scrollHeight, 200) + 'px';
    updateSendButton();
}

// Update send button state
function updateSendButton() {
    const hasText = messageInput.value.trim().length > 0;
    sendButton.disabled = !hasText || isTyping;
    sendButton.style.opacity = hasText && !isTyping ? '1' : '0.5';
}

// Handle keyboard input
function handleKeyPress(event) {
    if (event.key === 'Enter' && !event.shiftKey) {
        event.preventDefault();
        sendMessage();
    }
}

// Send message
function sendMessage() {
    const text = messageInput.value.trim();
    if (!text || isTyping) return;

    // Add user message
    addMessage(text, 'user');

    // Clear input
    messageInput.value = '';
    adjustTextarea();

    // Show typing indicator
    showTypingIndicator();

    // Simulate AI response
    setTimeout(() => {
        hideTypingIndicator();

        const responses = [
            "Понял ваш вопрос. Вот что я могу сказать по этому поводу...",
            "Интересная задача! Давайте разберем это пошагово.",
            "Хороший вопрос. Позвольте мне объяснить подробнее.",
            "Я помогу вам с этим. Вот мой ответ:",
            "Отлично! Вот что я думаю об этом:"
        ];

        const randomResponse = responses[Math.floor(Math.random() * responses.length)];
        addMessage(randomResponse, 'ai');

        // Update chat title if it's the first message
        updateChatTitle(text);

    }, 1000 + Math.random() * 2000);
}

// Add message to chat
function addMessage(text, sender) {
    const messageDiv = document.createElement('div');
    messageDiv.className = 'message';

    const avatar = document.createElement('div');
    avatar.className = 'message-avatar';
    avatar.textContent = sender === 'user' ? 'Вы' : 'AI';

    const content = document.createElement('div');
    content.className = 'message-content';

    // Handle line breaks
    const formattedText = text.replace(/\n/g, '<br>');
    content.innerHTML = formattedText;

    messageDiv.appendChild(avatar);
    messageDiv.appendChild(content);

    messages.appendChild(messageDiv);

    // Save to current chat
    chats[currentChatId].messages.push({ text, sender, timestamp: Date.now() });

    // Scroll to bottom
    messages.scrollTop = messages.scrollHeight;
}

// Show typing indicator
function showTypingIndicator() {
    isTyping = true;
    updateSendButton();

    const typingDiv = document.createElement('div');
    typingDiv.className = 'message typing-message';
    typingDiv.id = 'typing-indicator';

    const avatar = document.createElement('div');
    avatar.className = 'message-avatar';
    avatar.textContent = 'AI';

    const content = document.createElement('div');
    content.className = 'message-content typing-indicator';
    content.innerHTML = `
        <div class="typing-dots">
            <span></span>
            <span></span>
            <span></span>
        </div>
    `;

    typingDiv.appendChild(avatar);
    typingDiv.appendChild(content);

    messages.appendChild(typingDiv);
    messages.scrollTop = messages.scrollHeight;
}

// Hide typing indicator
function hideTypingIndicator() {
    isTyping = false;
    updateSendButton();

    const typingIndicator = document.getElementById('typing-indicator');
    if (typingIndicator) {
        typingIndicator.remove();
    }
}

// New chat
function newChat() {
    const newId = Date.now();
    chats[newId] = {
        id: newId,
        title: 'Новый чат',
        messages: []
    };

    switchToChat(newId);
    addChatToSidebar(newId, 'Новый чат');
}

// Switch to chat
function switchToChat(chatId) {
    currentChatId = chatId;

    // Update active chat in sidebar
    document.querySelectorAll('.chat-item').forEach(item => {
        item.classList.remove('active');
        if (item.dataset.chatId == chatId) {
            item.classList.add('active');
        }
    });

    // Load chat messages
    loadChatMessages(chatId);
}

// Load chat messages
function loadChatMessages(chatId) {
    messages.innerHTML = '';

    const chat = chats[chatId];
    if (chat && chat.messages) {
        chat.messages.forEach(msg => {
            addMessageToDOM(msg.text, msg.sender);
        });
    }
}

// Add message to DOM only (without saving)
function addMessageToDOM(text, sender) {
    const messageDiv = document.createElement('div');
    messageDiv.className = 'message';

    const avatar = document.createElement('div');
    avatar.className = 'message-avatar';
    avatar.textContent = sender === 'user' ? 'Вы' : 'AI';

    const content = document.createElement('div');
    content.className = 'message-content';
    content.innerHTML = text.replace(/\n/g, '<br>');

    messageDiv.appendChild(avatar);
    messageDiv.appendChild(content);

    messages.appendChild(messageDiv);
}

// Add chat to sidebar
function addChatToSidebar(chatId, title) {
    const chatItem = document.createElement('div');
    chatItem.className = 'chat-item active';
    chatItem.dataset.chatId = chatId;
    chatItem.onclick = () => switchToChat(chatId);

    chatItem.innerHTML = `
        <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
            <path d="M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
        </svg>
        <span>${title}</span>
    `;

    // Remove active from other chats
    document.querySelectorAll('.chat-item').forEach(item => {
        item.classList.remove('active');
    });

    chatList.insertBefore(chatItem, chatList.firstChild);
}

// Update chat title based on first message
function updateChatTitle(firstMessage) {
    const chat = chats[currentChatId];
    if (chat.messages.length === 1) { // First user message
        const title = firstMessage.length > 30 ?
            firstMessage.substring(0, 30) + '...' :
            firstMessage;

        chat.title = title;

        // Update sidebar
        const chatItem = document.querySelector(`[data-chat-id="${currentChatId}"] span`);
        if (chatItem) {
            chatItem.textContent = title;
        }
    }
}

// Event listeners
messageInput.addEventListener('input', adjustTextarea);

// Focus input when clicking anywhere in the input area
document.querySelector('.input-section').addEventListener('click', () => {
    messageInput.focus();
});
