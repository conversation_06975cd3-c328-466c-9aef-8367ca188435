<!DOCTYPE html>
<html lang="ru">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI Assistant - Умный помощник</title>
    <link rel="stylesheet" href="styles.css">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
</head>
<body>
    <div class="container">
        <!-- Header -->
        <header class="header">
            <div class="header-content">
                <div class="logo">
                    <i class="fas fa-robot"></i>
                    <span>AI Assistant</span>
                </div>
                <nav class="nav">
                    <a href="#" class="nav-link active">Чат</a>
                    <a href="#" class="nav-link">История</a>
                    <a href="#" class="nav-link">Настройки</a>
                </nav>
                <div class="user-menu">
                    <div class="user-avatar">
                        <i class="fas fa-user"></i>
                    </div>
                </div>
            </div>
        </header>

        <!-- Main Content -->
        <main class="main">
            <!-- Sidebar -->
            <aside class="sidebar">
                <div class="sidebar-header">
                    <button class="new-chat-btn">
                        <i class="fas fa-plus"></i>
                        Новый чат
                    </button>
                </div>
                <div class="chat-history">
                    <div class="chat-item active">
                        <i class="fas fa-message"></i>
                        <span>Помощь с кодом</span>
                        <button class="chat-options">
                            <i class="fas fa-ellipsis-h"></i>
                        </button>
                    </div>
                    <div class="chat-item">
                        <i class="fas fa-message"></i>
                        <span>Анализ данных</span>
                        <button class="chat-options">
                            <i class="fas fa-ellipsis-h"></i>
                        </button>
                    </div>
                    <div class="chat-item">
                        <i class="fas fa-message"></i>
                        <span>Создание контента</span>
                        <button class="chat-options">
                            <i class="fas fa-ellipsis-h"></i>
                        </button>
                    </div>
                </div>
            </aside>

            <!-- Chat Area -->
            <div class="chat-container">
                <div class="chat-header">
                    <h1>AI Assistant</h1>
                    <p>Ваш умный помощник готов к работе</p>
                </div>

                <div class="chat-messages" id="chatMessages">
                    <!-- Welcome Message -->
                    <div class="message ai-message">
                        <div class="message-avatar">
                            <i class="fas fa-robot"></i>
                        </div>
                        <div class="message-content">
                            <div class="message-text">
                                Привет! Я ваш AI помощник. Чем могу помочь сегодня? Я могу:
                                <ul>
                                    <li>Отвечать на вопросы</li>
                                    <li>Помогать с программированием</li>
                                    <li>Анализировать данные</li>
                                    <li>Создавать контент</li>
                                    <li>И многое другое!</li>
                                </ul>
                            </div>
                            <div class="message-time">Сейчас</div>
                        </div>
                    </div>
                </div>

                <!-- Quick Actions -->
                <div class="quick-actions">
                    <button class="quick-action" onclick="sendQuickMessage('Помоги с кодом')">
                        <i class="fas fa-code"></i>
                        Помощь с кодом
                    </button>
                    <button class="quick-action" onclick="sendQuickMessage('Объясни концепцию')">
                        <i class="fas fa-lightbulb"></i>
                        Объяснить концепцию
                    </button>
                    <button class="quick-action" onclick="sendQuickMessage('Создай контент')">
                        <i class="fas fa-pen"></i>
                        Создать контент
                    </button>
                    <button class="quick-action" onclick="sendQuickMessage('Анализируй данные')">
                        <i class="fas fa-chart-bar"></i>
                        Анализ данных
                    </button>
                </div>

                <!-- Input Area -->
                <div class="input-area">
                    <div class="input-container">
                        <button class="attach-btn" title="Прикрепить файл">
                            <i class="fas fa-paperclip"></i>
                        </button>
                        <textarea 
                            id="messageInput" 
                            placeholder="Введите ваше сообщение..." 
                            rows="1"
                            onkeydown="handleKeyDown(event)"
                        ></textarea>
                        <button class="send-btn" onclick="sendMessage()" id="sendBtn">
                            <i class="fas fa-paper-plane"></i>
                        </button>
                    </div>
                    <div class="input-footer">
                        <span class="input-hint">Нажмите Enter для отправки, Shift+Enter для новой строки</span>
                        <div class="model-selector">
                            <select id="modelSelect">
                                <option value="gpt-4">GPT-4</option>
                                <option value="claude">Claude</option>
                                <option value="gemini">Gemini</option>
                            </select>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <!-- Loading Overlay -->
    <div class="loading-overlay" id="loadingOverlay">
        <div class="loading-spinner">
            <div class="spinner"></div>
            <p>AI думает...</p>
        </div>
    </div>

    <script src="script.js"></script>
</body>
</html>
