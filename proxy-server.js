// Simple CORS proxy server for GigaChat API
// Run with: node proxy-server.js

const express = require('express');
const cors = require('cors');
const fetch = require('node-fetch');
const app = express();
const PORT = 3001;

// Enable CORS for all routes
app.use(cors());
app.use(express.json());

// Serve static files
app.use(express.static('.'));

// Proxy endpoint for GigaChat OAuth
app.post('/api/oauth', async (req, res) => {
    try {
        const response = await fetch('https://ngw.devices.sberbank.ru:9443/api/v2/oauth', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
                'Accept': 'application/json',
                'RqUID': generateUUID(),
                'Authorization': req.headers.authorization
            },
            body: 'scope=GIGACHAT_API_PERS'
        });

        const data = await response.json();
        res.json(data);
    } catch (error) {
        console.error('OAuth Error:', error);
        res.status(500).json({ error: error.message });
    }
});

// Proxy endpoint for GigaChat Chat Completions
app.post('/api/chat/completions', async (req, res) => {
    try {
        const response = await fetch('https://gigachat.devices.sberbank.ru/api/v1/chat/completions', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Accept': 'application/json',
                'Authorization': req.headers.authorization
            },
            body: JSON.stringify(req.body)
        });

        const data = await response.json();
        res.json(data);
    } catch (error) {
        console.error('Chat Completions Error:', error);
        res.status(500).json({ error: error.message });
    }
});

// Proxy endpoint for GigaChat Models
app.get('/api/models', async (req, res) => {
    try {
        const response = await fetch('https://gigachat.devices.sberbank.ru/api/v1/models', {
            method: 'GET',
            headers: {
                'Accept': 'application/json',
                'Authorization': req.headers.authorization
            }
        });

        const data = await response.json();
        res.json(data);
    } catch (error) {
        console.error('Models Error:', error);
        res.status(500).json({ error: error.message });
    }
});

function generateUUID() {
    return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
        const r = Math.random() * 16 | 0;
        const v = c == 'x' ? r : (r & 0x3 | 0x8);
        return v.toString(16);
    });
}

app.listen(PORT, () => {
    console.log(`Proxy server running on http://localhost:${PORT}`);
    console.log('Open http://localhost:3001 to access your ChatGPT clone');
});

// Package.json content for easy setup
const packageJson = {
    "name": "gigachat-proxy",
    "version": "1.0.0",
    "description": "CORS proxy for GigaChat API",
    "main": "proxy-server.js",
    "scripts": {
        "start": "node proxy-server.js",
        "dev": "nodemon proxy-server.js"
    },
    "dependencies": {
        "express": "^4.18.2",
        "cors": "^2.8.5",
        "node-fetch": "^2.6.7"
    },
    "devDependencies": {
        "nodemon": "^3.0.1"
    }
};

console.log('\nTo set up the proxy server:');
console.log('1. Create package.json with the following content:');
console.log(JSON.stringify(packageJson, null, 2));
console.log('\n2. Run: npm install');
console.log('3. Run: npm start');
