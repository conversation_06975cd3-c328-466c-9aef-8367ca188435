# ChatGPT Clone с GigaChat API

Современный веб-интерфейс для работы с GigaChat API от Сбера.

## 🚀 Возможности

- ✅ **Реальный ИИ** - интеграция с GigaChat API
- ✅ **Авторизация** - система входа и регистрации
- ✅ **Профили пользователей** - аватары, настройки, статистика
- ✅ **Управление чатами** - создание, переименование, удаление
- ✅ **Экспорт данных** - сохранение чатов в TXT/JSON
- ✅ **Темная тема** - переключение светлой/темной темы
- ✅ **Адаптивный дизайн** - работает на всех устройствах

## 🛠 Установка и запуск

### Вариант 1: С прокси-сервером (рекомендуется)

1. **Установите Node.js** (если не установлен)
   ```bash
   # Скачайте с https://nodejs.org/
   ```

2. **Создайте package.json**
   ```json
   {
     "name": "gigachat-proxy",
     "version": "1.0.0",
     "description": "CORS proxy for GigaChat API",
     "main": "proxy-server.js",
     "scripts": {
       "start": "node proxy-server.js",
       "dev": "nodemon proxy-server.js"
     },
     "dependencies": {
       "express": "^4.18.2",
       "cors": "^2.8.5",
       "node-fetch": "^2.6.7"
     },
     "devDependencies": {
       "nodemon": "^3.0.1"
     }
   }
   ```

3. **Установите зависимости**
   ```bash
   npm install
   ```

4. **Запустите прокси-сервер**
   ```bash
   npm start
   ```

5. **Откройте браузер**
   ```
   http://localhost:3001
   ```

### Вариант 2: Только фронтенд (демо-режим)

1. **Откройте файл index.html** в браузере
2. Приложение будет работать в демо-режиме без реального ИИ

## 🔑 API Ключ

API ключ GigaChat уже встроен в код:
```
ZWMyMzlkZjUtYzVhOS00OTExLThhMGUtMGMwNmNiMmZhNDg4OmYzNTgwMGQwLTIzNjYtNGI1OC05ZGI5LTBkOWI1NzNjMmE1Nw==
```

Если нужно изменить ключ, отредактируйте файл `gigachat-api.js`:
```javascript
this.apiKey = 'ВАШ_НОВЫЙ_КЛЮЧ';
```

## 📁 Структура проекта

```
AI bot/
├── index.html          # Главная страница чата
├── auth.html           # Страница авторизации
├── styles.css          # Стили для чата
├── auth.css            # Стили для авторизации
├── script.js           # Основная логика чата
├── auth.js             # Логика авторизации
├── gigachat-api.js     # Интеграция с GigaChat API
├── proxy-server.js     # CORS прокси-сервер
└── README.md           # Документация
```

## 🎯 Использование

### Первый запуск

1. Откройте `auth.html` или `http://localhost:3001`
2. Войдите с демо-аккаунтом:
   - **Email:** <EMAIL>
   - **Пароль:** demo123456
3. Или создайте новый аккаунт

### Основные функции

- **Отправка сообщений** - введите текст и нажмите Enter
- **Новый чат** - кнопка "Новый чат" в боковой панели
- **Управление чатами** - три точки рядом с названием чата
- **Настройки** - кнопка настроек внизу боковой панели
- **Профиль** - клик на аватар пользователя

### Горячие клавиши

- **Ctrl+K** - создать новый чат
- **Ctrl+R** - повторить последний неудачный запрос
- **Enter** - отправить сообщение
- **Shift+Enter** - новая строка

## 🔧 Настройки

В настройках доступны:

- **Модель ИИ** - выбор между GigaChat и GigaChat-Pro
- **Тема** - светлая или темная
- **Температура** - креативность ответов (0.1-1.0)
- **Тест подключения** - проверка работы API

## 📤 Экспорт данных

Экспорт чатов доступен в нескольких форматах:

- **TXT** - простой текстовый файл
- **JSON** - структурированные данные
- **Все чаты** - экспорт всех чатов одним файлом

## 🐛 Устранение неполадок

### Ошибка CORS
```
Ошибка сети. Для работы с GigaChat запустите прокси-сервер
```
**Решение:** Запустите прокси-сервер командой `npm start`

### Ошибка авторизации
```
Ошибка авторизации GigaChat. Проверьте API ключ
```
**Решение:** Проверьте правильность API ключа в `gigachat-api.js`

### Превышен лимит запросов
```
Превышен лимит запросов GigaChat
```
**Решение:** Подождите несколько минут перед следующим запросом

## 🔄 Обновления

Для получения обновлений:
1. Сохраните ваши данные (экспорт чатов)
2. Скачайте новую версию
3. Перенесите файлы данных

## 📞 Поддержка

При возникновении проблем:
1. Проверьте консоль браузера (F12)
2. Убедитесь, что прокси-сервер запущен
3. Проверьте подключение к интернету
4. Перезапустите прокси-сервер

## 📄 Лицензия

Проект создан в образовательных целях. 
GigaChat API принадлежит ПАО Сбербанк.

---

**Приятного использования! 🎉**
