// Check if user is already logged in
document.addEventListener('DOMContentLoaded', function() {
    const user = getCurrentUser();
    if (user) {
        // Redirect to main app
        window.location.href = 'index.html';
    }
});

// Show/Hide forms
function showLogin() {
    document.getElementById('loginForm').classList.remove('hidden');
    document.getElementById('registerForm').classList.add('hidden');
    document.getElementById('profileForm').classList.add('hidden');
    
    document.querySelector('.auth-subtitle').textContent = 'Добро пожаловать обратно';
}

function showRegister() {
    document.getElementById('loginForm').classList.add('hidden');
    document.getElementById('registerForm').classList.remove('hidden');
    document.getElementById('profileForm').classList.add('hidden');
    
    document.querySelector('.auth-subtitle').textContent = 'Создайте новый аккаунт';
}

function showProfile() {
    document.getElementById('loginForm').classList.add('hidden');
    document.getElementById('registerForm').classList.add('hidden');
    document.getElementById('profileForm').classList.remove('hidden');
}

// Handle login
function handleLogin(event) {
    event.preventDefault();
    
    const email = document.getElementById('loginEmail').value;
    const password = document.getElementById('loginPassword').value;
    const rememberMe = document.getElementById('rememberMe').checked;
    
    // Show loading state
    const submitBtn = event.target.querySelector('button[type="submit"]');
    submitBtn.classList.add('loading');
    submitBtn.disabled = true;
    
    // Simulate API call
    setTimeout(() => {
        const users = getStoredUsers();
        const user = users.find(u => u.email === email && u.password === password);
        
        if (user) {
            // Login successful
            setCurrentUser(user, rememberMe);
            window.location.href = 'index.html';
        } else {
            // Login failed
            alert('Неверный email или пароль');
            submitBtn.classList.remove('loading');
            submitBtn.disabled = false;
        }
    }, 1500);
}

// Handle registration
function handleRegister(event) {
    event.preventDefault();
    
    const name = document.getElementById('registerName').value;
    const email = document.getElementById('registerEmail').value;
    const password = document.getElementById('registerPassword').value;
    const confirmPassword = document.getElementById('confirmPassword').value;
    
    // Validate passwords match
    if (password !== confirmPassword) {
        alert('Пароли не совпадают');
        return;
    }
    
    // Show loading state
    const submitBtn = event.target.querySelector('button[type="submit"]');
    submitBtn.classList.add('loading');
    submitBtn.disabled = true;
    
    // Simulate API call
    setTimeout(() => {
        const users = getStoredUsers();
        
        // Check if user already exists
        if (users.find(u => u.email === email)) {
            alert('Пользователь с таким email уже существует');
            submitBtn.classList.remove('loading');
            submitBtn.disabled = false;
            return;
        }
        
        // Create new user
        const newUser = {
            id: Date.now(),
            name: name,
            email: email,
            password: password, // В реальном приложении пароль должен быть захеширован
            createdAt: new Date().toISOString(),
            profile: {
                displayName: name,
                avatar: null,
                bio: '',
                interests: []
            }
        };
        
        // Save user
        users.push(newUser);
        localStorage.setItem('users', JSON.stringify(users));
        
        // Set current user
        setCurrentUser(newUser, false);
        
        // Show profile setup
        showProfile();
        document.getElementById('displayName').value = name;
        
        submitBtn.classList.remove('loading');
        submitBtn.disabled = false;
    }, 1500);
}

// Handle profile setup
function handleProfileSetup(event) {
    event.preventDefault();
    
    const displayName = document.getElementById('displayName').value;
    const bio = document.getElementById('bio').value;
    const interests = Array.from(document.querySelectorAll('.interest-tag input:checked')).map(cb => cb.value);
    
    const user = getCurrentUser();
    if (user) {
        // Update user profile
        user.profile.displayName = displayName;
        user.profile.bio = bio;
        user.profile.interests = interests;
        
        // Update stored user
        const users = getStoredUsers();
        const userIndex = users.findIndex(u => u.id === user.id);
        if (userIndex !== -1) {
            users[userIndex] = user;
            localStorage.setItem('users', JSON.stringify(users));
        }
        
        // Update current user
        setCurrentUser(user, true);
        
        // Redirect to main app
        window.location.href = 'index.html';
    }
}

// Skip profile setup
function skipProfile() {
    window.location.href = 'index.html';
}

// Google login (mock)
function loginWithGoogle() {
    // In a real app, this would integrate with Google OAuth
    alert('Google авторизация будет добавлена в следующей версии');
}

// Avatar preview
function previewAvatar(event) {
    const file = event.target.files[0];
    if (file) {
        const reader = new FileReader();
        reader.onload = function(e) {
            const preview = document.getElementById('avatarPreview');
            preview.innerHTML = `<img src="${e.target.result}" alt="Avatar">`;
            
            // Save avatar to current user
            const user = getCurrentUser();
            if (user) {
                user.profile.avatar = e.target.result;
                setCurrentUser(user, true);
            }
        };
        reader.readAsDataURL(file);
    }
}

// Utility functions
function getStoredUsers() {
    const users = localStorage.getItem('users');
    return users ? JSON.parse(users) : [];
}

function getCurrentUser() {
    const user = localStorage.getItem('currentUser') || sessionStorage.getItem('currentUser');
    return user ? JSON.parse(user) : null;
}

function setCurrentUser(user, remember = false) {
    const storage = remember ? localStorage : sessionStorage;
    storage.setItem('currentUser', JSON.stringify(user));
    
    // Also save to localStorage for persistence across sessions if remember is true
    if (remember) {
        localStorage.setItem('currentUser', JSON.stringify(user));
    }
}

// Password validation
document.getElementById('registerPassword')?.addEventListener('input', function() {
    const password = this.value;
    const hint = this.parentElement.querySelector('.password-hint');
    
    if (password.length < 8) {
        hint.style.color = '#ef4444';
        hint.textContent = 'Пароль должен содержать минимум 8 символов';
    } else {
        hint.style.color = '#10b981';
        hint.textContent = 'Пароль подходит ✓';
    }
});

// Confirm password validation
document.getElementById('confirmPassword')?.addEventListener('input', function() {
    const password = document.getElementById('registerPassword').value;
    const confirmPassword = this.value;
    
    if (confirmPassword && password !== confirmPassword) {
        this.style.borderColor = '#ef4444';
    } else {
        this.style.borderColor = '#d1d5db';
    }
});

// Demo users for testing
function createDemoUsers() {
    const users = getStoredUsers();
    if (users.length === 0) {
        const demoUsers = [
            {
                id: 1,
                name: 'Демо Пользователь',
                email: '<EMAIL>',
                password: 'demo123456',
                createdAt: new Date().toISOString(),
                profile: {
                    displayName: 'Демо Пользователь',
                    avatar: null,
                    bio: 'Это демо-аккаунт для тестирования',
                    interests: ['programming', 'design']
                }
            }
        ];
        localStorage.setItem('users', JSON.stringify(demoUsers));
    }
}

// Create demo users on page load
createDemoUsers();

// Add some visual feedback for form interactions
document.addEventListener('DOMContentLoaded', function() {
    // Add focus effects to inputs
    const inputs = document.querySelectorAll('input, textarea');
    inputs.forEach(input => {
        input.addEventListener('focus', function() {
            this.parentElement.classList.add('focused');
        });
        
        input.addEventListener('blur', function() {
            this.parentElement.classList.remove('focused');
        });
    });
    
    // Add ripple effect to buttons
    const buttons = document.querySelectorAll('.auth-btn');
    buttons.forEach(button => {
        button.addEventListener('click', function(e) {
            const ripple = document.createElement('span');
            const rect = this.getBoundingClientRect();
            const size = Math.max(rect.width, rect.height);
            const x = e.clientX - rect.left - size / 2;
            const y = e.clientY - rect.top - size / 2;
            
            ripple.style.cssText = `
                position: absolute;
                width: ${size}px;
                height: ${size}px;
                left: ${x}px;
                top: ${y}px;
                background: rgba(255, 255, 255, 0.3);
                border-radius: 50%;
                transform: scale(0);
                animation: ripple 0.6s linear;
                pointer-events: none;
            `;
            
            this.style.position = 'relative';
            this.style.overflow = 'hidden';
            this.appendChild(ripple);
            
            setTimeout(() => ripple.remove(), 600);
        });
    });
});

// Add ripple animation
const style = document.createElement('style');
style.textContent = `
    @keyframes ripple {
        to {
            transform: scale(4);
            opacity: 0;
        }
    }
    
    .form-group.focused label {
        color: #4f46e5;
    }
`;
document.head.appendChild(style);
